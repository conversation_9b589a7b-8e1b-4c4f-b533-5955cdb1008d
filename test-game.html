<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Water Polo Clash - Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        #gameFrame {
            width: 100%;
            height: 600px;
            border: 2px solid #007bff;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <h1>🏊‍♂️ Water Polo Clash - Game Test</h1>
    
    <div class="test-section">
        <h2>🔧 System Tests</h2>
        <div id="systemTests">
            <div class="status info">Running system tests...</div>
        </div>
        <button onclick="runSystemTests()">Run System Tests</button>
    </div>

    <div class="test-section">
        <h2>🎮 Game Preview</h2>
        <p>Click the button below to load the game in an iframe for testing:</p>
        <button onclick="loadGame()">Load Game</button>
        <button onclick="testControls()">Test Controls</button>
        <div id="gameContainer" style="margin-top: 20px;"></div>
    </div>

    <div class="test-section">
        <h2>📋 Test Checklist</h2>
        <div id="testChecklist">
            <label><input type="checkbox" id="test1"> Game loads without errors</label><br>
            <label><input type="checkbox" id="test2"> Main menu appears</label><br>
            <label><input type="checkbox" id="test3"> Single player mode works</label><br>
            <label><input type="checkbox" id="test4"> Arcade mode works</label><br>
            <label><input type="checkbox" id="test5"> Player movement (WASD)</label><br>
            <label><input type="checkbox" id="test6"> Ball passing (SPACE)</label><br>
            <label><input type="checkbox" id="test7"> Player switching (TAB)</label><br>
            <label><input type="checkbox" id="test8"> AI opponents move</label><br>
            <label><input type="checkbox" id="test9"> Score tracking works</label><br>
            <label><input type="checkbox" id="test10"> Shot clock functions</label><br>
        </div>
        <button onclick="checkAllTests()">Mark All Complete</button>
    </div>

    <div class="test-section">
        <h2>🚀 Quick Start Guide</h2>
        <ol>
            <li><strong>Start Server:</strong> <code>npm run dev</code></li>
            <li><strong>Open Game:</strong> <a href="http://127.0.0.1:8080" target="_blank">http://127.0.0.1:8080</a></li>
            <li><strong>Controls:</strong>
                <ul>
                    <li>WASD - Move player</li>
                    <li>SPACE - Pass/Shoot</li>
                    <li>TAB - Switch player</li>
                    <li>ESC - Pause</li>
                </ul>
            </li>
            <li><strong>Game Modes:</strong>
                <ul>
                    <li>Single Player - Standard water polo vs AI</li>
                    <li>Arcade Mode - Enhanced with power-ups</li>
                </ul>
            </li>
        </ol>
    </div>

    <script>
        function runSystemTests() {
            const container = document.getElementById('systemTests');
            container.innerHTML = '<div class="status info">Running tests...</div>';
            
            setTimeout(() => {
                let html = '';
                
                // Test 1: Check if server is running
                fetch('http://127.0.0.1:8080/js/config.js')
                    .then(response => {
                        if (response.ok) {
                            html += '<div class="status success">✅ Server is running on port 8080</div>';
                        } else {
                            html += '<div class="status error">❌ Server not responding</div>';
                        }
                        return fetch('http://127.0.0.1:8080/js/main.js');
                    })
                    .then(response => {
                        if (response.ok) {
                            html += '<div class="status success">✅ Game files are accessible</div>';
                        } else {
                            html += '<div class="status error">❌ Game files not found</div>';
                        }
                        
                        // Test browser compatibility
                        if (typeof Phaser !== 'undefined') {
                            html += '<div class="status success">✅ Phaser 3 is available</div>';
                        } else {
                            html += '<div class="status info">ℹ️ Phaser 3 will load from CDN</div>';
                        }
                        
                        // Test device capabilities
                        if ('ontouchstart' in window) {
                            html += '<div class="status success">✅ Touch controls supported</div>';
                        } else {
                            html += '<div class="status info">ℹ️ Desktop mode (keyboard controls)</div>';
                        }
                        
                        html += '<div class="status success">✅ All system tests passed!</div>';
                        container.innerHTML = html;
                    })
                    .catch(error => {
                        html += '<div class="status error">❌ Error: ' + error.message + '</div>';
                        container.innerHTML = html;
                    });
            }, 500);
        }

        function loadGame() {
            const container = document.getElementById('gameContainer');
            container.innerHTML = '<iframe id="gameFrame" src="http://127.0.0.1:8080"></iframe>';
        }

        function testControls() {
            alert('Game Controls Test:\n\n' +
                  '1. WASD - Move your player around\n' +
                  '2. SPACE - Pass ball or shoot at goal\n' +
                  '3. TAB - Switch to different player\n' +
                  '4. ESC - Pause/unpause game\n' +
                  '5. Mouse Click - Direct pass/shoot\n\n' +
                  'Try these controls in the game!');
        }

        function checkAllTests() {
            for (let i = 1; i <= 10; i++) {
                document.getElementById('test' + i).checked = true;
            }
            alert('All tests marked as complete! 🎉\n\nThe Water Polo Clash game is ready to play!');
        }

        // Auto-run system tests on page load
        window.onload = function() {
            runSystemTests();
        };
    </script>
</body>
</html>
