<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="description" content="Water Polo Clash - A 2D top-down water polo game with realistic rules and AI opponents">
    <meta name="keywords" content="water polo, game, sports, phaser3, javascript, browser game">
    <meta name="author" content="Water Polo Clash Team">
    <title>Water Polo Clash</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Arial', sans-serif;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        #game-container {
            position: relative;
            border: 3px solid #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
            background: #000;
        }

        #game-canvas {
            display: block;
            border-radius: 7px;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-align: center;
            z-index: 1000;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #fff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Mobile optimizations */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            #game-container {
                width: 100%;
                height: 100%;
                border-radius: 5px;
            }
        }

        /* Touch controls overlay */
        .touch-controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            display: none;
            z-index: 100;
        }

        @media (max-width: 768px) {
            .touch-controls {
                display: flex;
                justify-content: space-between;
                align-items: flex-end;
            }
        }

        .control-pad {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            position: relative;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .action-btn {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            color: white;
            font-size: 12px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            user-select: none;
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div class="loading" id="loading">
            Loading Water Polo Clash...
        </div>
        <canvas id="game-canvas"></canvas>
        
        <!-- Touch controls for mobile -->
        <div class="touch-controls">
            <div class="control-pad" id="movement-pad"></div>
            <div class="action-buttons">
                <div class="action-btn" id="pass-btn">PASS</div>
                <div class="action-btn" id="shoot-btn">SHOOT</div>
                <div class="action-btn" id="switch-btn">SWITCH</div>
            </div>
        </div>
    </div>

    <!-- Phaser 3 CDN -->
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    
    <!-- Game scripts -->
    <script src="js/config.js"></script>
    <script src="js/scenes/PreloadScene.js"></script>
    <script src="js/scenes/MainMenuScene.js"></script>
    <script src="js/scenes/GameScene.js"></script>
    <script src="js/entities/Player.js"></script>
    <script src="js/entities/Ball.js"></script>
    <script src="js/systems/InputManager.js"></script>
    <script src="js/systems/GameRules.js"></script>
    <script src="js/systems/AIManager.js"></script>
    <script src="js/systems/PowerUpManager.js"></script>
    <script src="js/ui/HUD.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
