// Game configuration and constants
const GameConfig = {
    // Display settings
    CANVAS_WIDTH: 1024,
    CANVAS_HEIGHT: 640,
    
    // Pool dimensions (scaled from real 25.6m × 20m)
    POOL: {
        WIDTH: 960,
        HEIGHT: 600,
        REAL_WIDTH: 25.6, // meters
        REAL_HEIGHT: 20,  // meters
        SCALE: 960 / 25.6, // pixels per meter
        
        // Zone markings
        GOAL_WIDTH: 120,
        GOAL_HEIGHT: 20,
        TWO_METER_LINE: 2 * (960 / 25.6), // 2m from goal line
        SIX_METER_LINE: 6 * (960 / 25.6), // 6m from goal line
        
        // Colors
        WATER_COLOR: 0x4A90E2,
        LANE_COLOR: 0xFFFFFF,
        GOAL_COLOR: 0xFF6B6B,
        ZONE_COLOR: 0xFFE66D
    },
    
    // Game rules
    RULES: {
        PLAYERS_PER_TEAM: 7,
        QUARTERS: 4,
        QUARTER_DURATION: 120, // seconds (2 minutes default)
        SHOT_CLOCK_NORMAL: 28, // seconds
        SHOT_CLOCK_REBOUND: 18, // seconds
        EXCLUSION_TIME: 20, // seconds
        
        // Fouls
        MAJOR_FOUL_EXCLUSION: true,
        MINOR_FOUL_TURNOVER: true
    },
    
    // Player settings
    PLAYER: {
        RADIUS: 12,
        MAX_SPEED: 120, // pixels per second
        ACCELERATION: 300,
        DRAG: 0.85,
        
        // Team colors
        TEAM_1_COLOR: 0x3498DB, // Blue
        TEAM_2_COLOR: 0xE74C3C, // Red
        GOALKEEPER_COLOR: 0xF39C12, // Orange
        
        // Numbers
        SHOW_NUMBERS: true,
        NUMBER_SIZE: 10
    },
    
    // Ball settings
    BALL: {
        RADIUS: 8,
        COLOR: 0xFFD700, // Gold
        MAX_SPEED: 200,
        DRAG: 0.95,
        BOUNCE: 0.7,
        
        // Physics
        PASS_FORCE: 150,
        SHOOT_FORCE: 250,
        WATER_RESISTANCE: 0.98
    },
    
    // Input settings
    INPUT: {
        KEYBOARD_ENABLED: true,
        TOUCH_ENABLED: true,
        
        // Keyboard controls
        MOVE_UP: 'W',
        MOVE_DOWN: 'S',
        MOVE_LEFT: 'A',
        MOVE_RIGHT: 'D',
        ACTION: 'SPACE',
        SWITCH_PLAYER: 'TAB',
        
        // Touch sensitivity
        TOUCH_DEADZONE: 20,
        TOUCH_MAX_DISTANCE: 50
    },
    
    // AI settings
    AI: {
        REACTION_TIME: 0.2, // seconds
        SKILL_LEVEL: 0.7, // 0-1 scale
        POSITIONING_UPDATE_RATE: 0.1, // seconds
        
        // Behaviors
        AGGRESSIVE: 0.6,
        DEFENSIVE: 0.8,
        GOALKEEPER_SKILL: 0.9
    },
    
    // UI settings
    UI: {
        HUD_HEIGHT: 80,
        FONT_FAMILY: 'Arial',
        FONT_SIZE: 16,
        
        // Colors
        PRIMARY_COLOR: 0x2C3E50,
        SECONDARY_COLOR: 0x34495E,
        TEXT_COLOR: 0xFFFFFF,
        ACCENT_COLOR: 0x3498DB
    },
    
    // Arcade mode settings
    ARCADE: {
        POWER_UPS_ENABLED: true,
        TURBO_DURATION: 5, // seconds
        TURBO_SPEED_MULTIPLIER: 1.5,
        FAST_SHOT_DURATION: 3, // seconds
        FAST_SHOT_SPEED_MULTIPLIER: 1.8,
        
        POWER_UP_SPAWN_RATE: 15 // seconds
    },
    
    // Performance settings
    PERFORMANCE: {
        TARGET_FPS: 60,
        PHYSICS_STEPS: 60,
        DEBUG_MODE: false,
        MOBILE_OPTIMIZATIONS: true,
        REDUCE_PARTICLES_ON_MOBILE: true,
        LOWER_QUALITY_ON_MOBILE: true
    }
};

// Utility functions
const GameUtils = {
    // Convert meters to pixels
    metersToPixels(meters) {
        return meters * GameConfig.POOL.SCALE;
    },
    
    // Convert pixels to meters
    pixelsToMeters(pixels) {
        return pixels / GameConfig.POOL.SCALE;
    },
    
    // Get distance between two points
    getDistance(x1, y1, x2, y2) {
        return Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
    },
    
    // Get angle between two points
    getAngle(x1, y1, x2, y2) {
        return Math.atan2(y2 - y1, x2 - x1);
    },
    
    // Clamp value between min and max
    clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    },
    
    // Linear interpolation
    lerp(start, end, factor) {
        return start + (end - start) * factor;
    },
    
    // Check if point is inside rectangle
    pointInRect(x, y, rectX, rectY, rectWidth, rectHeight) {
        return x >= rectX && x <= rectX + rectWidth && 
               y >= rectY && y <= rectY + rectHeight;
    },
    
    // Format time as MM:SS
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
};

// Game states
const GameStates = {
    MENU: 'menu',
    PLAYING: 'playing',
    PAUSED: 'paused',
    QUARTER_BREAK: 'quarter_break',
    GAME_OVER: 'game_over',
    FOUL: 'foul',
    EXCLUSION: 'exclusion'
};

// Team definitions
const Teams = {
    TEAM_1: 0,
    TEAM_2: 1,
    NEUTRAL: -1
};

// Player positions
const PlayerPositions = {
    GOALKEEPER: 0,
    CENTER_BACK: 1,
    LEFT_BACK: 2,
    RIGHT_BACK: 3,
    CENTER_FORWARD: 4,
    LEFT_FORWARD: 5,
    RIGHT_FORWARD: 6
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GameConfig, GameUtils, GameStates, Teams, PlayerPositions };
}
