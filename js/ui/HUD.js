class HUD {
    constructor(scene) {
        this.scene = scene;
        
        // UI elements
        this.hudBackground = null;
        this.scoreText = null;
        this.timerText = null;
        this.quarterText = null;
        this.shotClockText = null;
        this.possessionIndicator = null;
        this.exclusionList = null;
        this.pauseOverlay = null;
        
        // UI containers
        this.hudContainer = null;
        this.gameOverContainer = null;
        
        // UI state
        this.isVisible = true;
        this.lastUpdate = 0;
    }
    
    create() {
        const width = this.scene.cameras.main.width;
        const height = this.scene.cameras.main.height;
        
        // Create HUD container
        this.hudContainer = this.scene.add.container(0, 0);
        this.hudContainer.setDepth(1000); // Ensure HUD is on top
        
        // HUD background
        this.hudBackground = this.scene.add.image(width / 2, GameConfig.UI.HUD_HEIGHT / 2, 'hud-bg');
        this.hudContainer.add(this.hudBackground);
        
        // Score display
        this.createScoreDisplay();
        
        // Timer display
        this.createTimerDisplay();
        
        // Shot clock display
        this.createShotClockDisplay();
        
        // Possession indicator
        this.createPossessionIndicator();
        
        // Exclusion display
        this.createExclusionDisplay();
        
        // Controls hint
        this.createControlsHint();
        
        console.log('HUD created successfully');
    }
    
    createScoreDisplay() {
        const width = this.scene.cameras.main.width;
        
        // Team 1 score
        this.team1ScoreText = this.scene.add.text(width * 0.2, 25, '0', {
            fontSize: '32px',
            fontFamily: GameConfig.UI.FONT_FAMILY,
            color: '#3498DB',
            fontStyle: 'bold'
        }).setOrigin(0.5);
        
        // VS separator
        this.vsText = this.scene.add.text(width * 0.5, 25, 'VS', {
            fontSize: '20px',
            fontFamily: GameConfig.UI.FONT_FAMILY,
            color: '#ffffff',
            fontStyle: 'bold'
        }).setOrigin(0.5);
        
        // Team 2 score
        this.team2ScoreText = this.scene.add.text(width * 0.8, 25, '0', {
            fontSize: '32px',
            fontFamily: GameConfig.UI.FONT_FAMILY,
            color: '#E74C3C',
            fontStyle: 'bold'
        }).setOrigin(0.5);
        
        // Team labels
        this.team1Label = this.scene.add.text(width * 0.2, 50, 'PLAYER', {
            fontSize: '12px',
            fontFamily: GameConfig.UI.FONT_FAMILY,
            color: '#3498DB',
            fontStyle: 'bold'
        }).setOrigin(0.5);
        
        this.team2Label = this.scene.add.text(width * 0.8, 50, 'AI', {
            fontSize: '12px',
            fontFamily: GameConfig.UI.FONT_FAMILY,
            color: '#E74C3C',
            fontStyle: 'bold'
        }).setOrigin(0.5);
        
        this.hudContainer.add([
            this.team1ScoreText, this.team2ScoreText, this.vsText,
            this.team1Label, this.team2Label
        ]);
    }
    
    createTimerDisplay() {
        const width = this.scene.cameras.main.width;
        
        // Game timer
        this.timerText = this.scene.add.text(width * 0.5, 45, '02:00', {
            fontSize: '24px',
            fontFamily: GameConfig.UI.FONT_FAMILY,
            color: '#ffffff',
            fontStyle: 'bold'
        }).setOrigin(0.5);
        
        // Quarter display
        this.quarterText = this.scene.add.text(width * 0.5, 65, 'Q1', {
            fontSize: '14px',
            fontFamily: GameConfig.UI.FONT_FAMILY,
            color: '#ffffff'
        }).setOrigin(0.5);
        
        this.hudContainer.add([this.timerText, this.quarterText]);
    }
    
    createShotClockDisplay() {
        const width = this.scene.cameras.main.width;
        
        // Shot clock background
        this.shotClockBg = this.scene.add.circle(width - 60, 40, 25, 0x2C3E50);
        this.shotClockBorder = this.scene.add.circle(width - 60, 40, 25);
        this.shotClockBorder.setStrokeStyle(3, 0xFFFFFF);
        
        // Shot clock text
        this.shotClockText = this.scene.add.text(width - 60, 40, '28', {
            fontSize: '18px',
            fontFamily: GameConfig.UI.FONT_FAMILY,
            color: '#ffffff',
            fontStyle: 'bold'
        }).setOrigin(0.5);
        
        // Shot clock label
        this.shotClockLabel = this.scene.add.text(width - 60, 60, 'SHOT', {
            fontSize: '10px',
            fontFamily: GameConfig.UI.FONT_FAMILY,
            color: '#ffffff'
        }).setOrigin(0.5);
        
        this.hudContainer.add([
            this.shotClockBg, this.shotClockBorder, 
            this.shotClockText, this.shotClockLabel
        ]);
    }
    
    createPossessionIndicator() {
        // Possession arrow
        this.possessionArrow = this.scene.add.triangle(100, 40, 0, 10, 20, 0, 0, -10, 0x3498DB);
        this.possessionText = this.scene.add.text(130, 40, 'POSSESSION', {
            fontSize: '12px',
            fontFamily: GameConfig.UI.FONT_FAMILY,
            color: '#ffffff'
        }).setOrigin(0, 0.5);
        
        this.hudContainer.add([this.possessionArrow, this.possessionText]);
    }
    
    createExclusionDisplay() {
        const width = this.scene.cameras.main.width;
        
        // Exclusion container
        this.exclusionContainer = this.scene.add.container(width - 200, 10);
        this.hudContainer.add(this.exclusionContainer);
        
        // Will be populated dynamically
        this.exclusionTexts = [];
    }
    
    createControlsHint() {
        const height = this.scene.cameras.main.height;
        
        // Only show on desktop
        if (!window.waterPoloGame || !window.waterPoloGame.isMobile) {
            this.controlsText = this.scene.add.text(10, height - 30, 
                'WASD: Move | SPACE: Action | TAB: Switch | ESC: Pause', {
                fontSize: '12px',
                fontFamily: GameConfig.UI.FONT_FAMILY,
                color: '#ffffff',
                alpha: 0.7
            });
            
            this.hudContainer.add(this.controlsText);
        }
    }
    
    update() {
        if (!this.isVisible) return;
        
        // Update score
        this.updateScore();
        
        // Update timer
        this.updateTimer();
        
        // Update shot clock
        this.updateShotClock();
        
        // Update possession
        this.updatePossession();
        
        // Update exclusions
        this.updateExclusions();
    }
    
    updateScore() {
        if (this.team1ScoreText && this.team2ScoreText) {
            this.team1ScoreText.setText(this.scene.score[0].toString());
            this.team2ScoreText.setText(this.scene.score[1].toString());
        }
    }
    
    updateTimer() {
        if (this.timerText && this.quarterText) {
            this.timerText.setText(GameUtils.formatTime(this.scene.gameTimer));
            this.quarterText.setText(`Q${this.scene.currentQuarter}`);
            
            // Change color when time is running low
            if (this.scene.gameTimer < 30) {
                this.timerText.setColor('#E74C3C');
            } else {
                this.timerText.setColor('#ffffff');
            }
        }
    }
    
    updateShotClock() {
        if (this.shotClockText && this.shotClockBg) {
            this.shotClockText.setText(Math.ceil(this.scene.shotClock).toString());
            
            // Change colors based on time left
            if (this.scene.shotClock <= 5) {
                this.shotClockBg.setFillStyle(0xE74C3C);
                this.shotClockText.setColor('#ffffff');
            } else if (this.scene.shotClock <= 10) {
                this.shotClockBg.setFillStyle(0xF39C12);
                this.shotClockText.setColor('#ffffff');
            } else {
                this.shotClockBg.setFillStyle(0x2C3E50);
                this.shotClockText.setColor('#ffffff');
            }
            
            // Hide if not active
            const alpha = this.scene.shotClockActive ? 1 : 0.3;
            this.shotClockBg.setAlpha(alpha);
            this.shotClockText.setAlpha(alpha);
            this.shotClockBorder.setAlpha(alpha);
            this.shotClockLabel.setAlpha(alpha);
        }
    }
    
    updatePossession() {
        if (this.possessionArrow) {
            const possession = this.scene.gameRules.currentPossession;
            
            if (possession === Teams.TEAM_1) {
                this.possessionArrow.setFillStyle(0x3498DB);
                this.possessionArrow.setRotation(0);
            } else {
                this.possessionArrow.setFillStyle(0xE74C3C);
                this.possessionArrow.setRotation(Math.PI);
            }
        }
    }
    
    updateExclusions() {
        // Clear existing exclusion texts
        this.exclusionTexts.forEach(text => text.destroy());
        this.exclusionTexts = [];
        
        // Add current exclusions
        const exclusions = this.scene.gameRules.exclusions;
        exclusions.forEach((exclusion, index) => {
            const timeLeft = Math.ceil(exclusion.timeLeft);
            const player = exclusion.player;
            const teamColor = player.team === Teams.TEAM_1 ? '#3498DB' : '#E74C3C';
            
            const exclusionText = this.scene.add.text(0, index * 20, 
                `P${player.number}: ${timeLeft}s`, {
                fontSize: '12px',
                fontFamily: GameConfig.UI.FONT_FAMILY,
                color: teamColor,
                fontStyle: 'bold'
            });
            
            this.exclusionContainer.add(exclusionText);
            this.exclusionTexts.push(exclusionText);
        });
    }
    
    // Game state overlays
    showPauseOverlay() {
        if (this.pauseOverlay) return;
        
        const width = this.scene.cameras.main.width;
        const height = this.scene.cameras.main.height;
        
        this.pauseOverlay = this.scene.add.container(0, 0);
        this.pauseOverlay.setDepth(2000);
        
        // Semi-transparent background
        const pauseBg = this.scene.add.rectangle(width / 2, height / 2, width, height, 0x000000, 0.7);
        
        // Pause text
        const pauseText = this.scene.add.text(width / 2, height / 2, 'PAUSED', {
            fontSize: '48px',
            fontFamily: GameConfig.UI.FONT_FAMILY,
            color: '#ffffff',
            fontStyle: 'bold'
        }).setOrigin(0.5);
        
        // Instructions
        const instructionText = this.scene.add.text(width / 2, height / 2 + 60, 
            'Press ESC to resume', {
            fontSize: '20px',
            fontFamily: GameConfig.UI.FONT_FAMILY,
            color: '#ffffff'
        }).setOrigin(0.5);
        
        this.pauseOverlay.add([pauseBg, pauseText, instructionText]);
    }
    
    hidePauseOverlay() {
        if (this.pauseOverlay) {
            this.pauseOverlay.destroy();
            this.pauseOverlay = null;
        }
    }
    
    showQuarterBreak(quarter) {
        const width = this.scene.cameras.main.width;
        const height = this.scene.cameras.main.height;
        
        const quarterOverlay = this.scene.add.container(0, 0);
        quarterOverlay.setDepth(2000);
        
        // Background
        const bg = this.scene.add.rectangle(width / 2, height / 2, width, height, 0x2C3E50, 0.9);
        
        // Quarter text
        const quarterText = this.scene.add.text(width / 2, height / 2 - 40, 
            `END OF QUARTER ${quarter - 1}`, {
            fontSize: '36px',
            fontFamily: GameConfig.UI.FONT_FAMILY,
            color: '#ffffff',
            fontStyle: 'bold'
        }).setOrigin(0.5);
        
        // Score
        const scoreText = this.scene.add.text(width / 2, height / 2, 
            `${this.scene.score[0]} - ${this.scene.score[1]}`, {
            fontSize: '48px',
            fontFamily: GameConfig.UI.FONT_FAMILY,
            color: '#3498DB',
            fontStyle: 'bold'
        }).setOrigin(0.5);
        
        // Next quarter
        const nextText = this.scene.add.text(width / 2, height / 2 + 60, 
            quarter <= GameConfig.RULES.QUARTERS ? `QUARTER ${quarter} STARTING...` : 'GAME OVER', {
            fontSize: '20px',
            fontFamily: GameConfig.UI.FONT_FAMILY,
            color: '#ffffff'
        }).setOrigin(0.5);
        
        quarterOverlay.add([bg, quarterText, scoreText, nextText]);
        
        // Auto-remove after 3 seconds
        this.scene.time.delayedCall(3000, () => {
            quarterOverlay.destroy();
        });
    }
    
    showGameOver(winner) {
        const width = this.scene.cameras.main.width;
        const height = this.scene.cameras.main.height;
        
        this.gameOverContainer = this.scene.add.container(0, 0);
        this.gameOverContainer.setDepth(2000);
        
        // Background
        const bg = this.scene.add.rectangle(width / 2, height / 2, width, height, 0x000000, 0.8);
        
        // Game over text
        const gameOverText = this.scene.add.text(width / 2, height / 2 - 80, 'GAME OVER', {
            fontSize: '48px',
            fontFamily: GameConfig.UI.FONT_FAMILY,
            color: '#ffffff',
            fontStyle: 'bold'
        }).setOrigin(0.5);
        
        // Winner text
        const winnerColor = winner === Teams.TEAM_1 ? '#3498DB' : '#E74C3C';
        const winnerName = winner === Teams.TEAM_1 ? 'PLAYER WINS!' : 'AI WINS!';
        const winnerText = this.scene.add.text(width / 2, height / 2 - 20, winnerName, {
            fontSize: '32px',
            fontFamily: GameConfig.UI.FONT_FAMILY,
            color: winnerColor,
            fontStyle: 'bold'
        }).setOrigin(0.5);
        
        // Final score
        const finalScoreText = this.scene.add.text(width / 2, height / 2 + 20, 
            `Final Score: ${this.scene.score[0]} - ${this.scene.score[1]}`, {
            fontSize: '24px',
            fontFamily: GameConfig.UI.FONT_FAMILY,
            color: '#ffffff'
        }).setOrigin(0.5);
        
        // Return to menu
        const menuText = this.scene.add.text(width / 2, height / 2 + 80, 
            'Returning to menu...', {
            fontSize: '16px',
            fontFamily: GameConfig.UI.FONT_FAMILY,
            color: '#ffffff',
            alpha: 0.7
        }).setOrigin(0.5);
        
        this.gameOverContainer.add([bg, gameOverText, winnerText, finalScoreText, menuText]);
    }
    
    // Utility methods
    setVisible(visible) {
        this.isVisible = visible;
        if (this.hudContainer) {
            this.hudContainer.setVisible(visible);
        }
    }
    
    destroy() {
        if (this.hudContainer) {
            this.hudContainer.destroy();
        }
        if (this.gameOverContainer) {
            this.gameOverContainer.destroy();
        }
        if (this.pauseOverlay) {
            this.pauseOverlay.destroy();
        }
    }
}
