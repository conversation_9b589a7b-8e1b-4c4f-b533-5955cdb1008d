class MainMenuScene extends Phaser.Scene {
    constructor() {
        super({ key: 'MainMenuScene' });
    }
    
    create() {
        const width = this.cameras.main.width;
        const height = this.cameras.main.height;
        
        // Background
        this.add.rectangle(width / 2, height / 2, width, height, 0x1e3c72);
        
        // Add animated water effect
        this.createWaterEffect();
        
        // Title
        this.createTitle();
        
        // Menu options
        this.createMenu();
        
        // Instructions
        this.createInstructions();
        
        // Version info
        this.add.text(width - 10, height - 10, 'v1.0.0', {
            fontSize: '12px',
            fontFamily: 'Arial',
            color: '#ffffff',
            alpha: 0.7
        }).setOrigin(1, 1);
    }
    
    createWaterEffect() {
        // Simple animated background to simulate water
        const width = this.cameras.main.width;
        const height = this.cameras.main.height;
        
        // Create multiple layers of moving rectangles to simulate water
        for (let i = 0; i < 5; i++) {
            const waterLayer = this.add.graphics();
            waterLayer.fillStyle(0x4A90E2, 0.1 + i * 0.05);
            waterLayer.fillRect(0, 0, width, height);
            
            // Animate the layer
            this.tweens.add({
                targets: waterLayer,
                alpha: 0.05 + i * 0.02,
                duration: 2000 + i * 500,
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
        }
    }
    
    createTitle() {
        const width = this.cameras.main.width;
        
        // Main title
        const title = this.add.text(width / 2, 120, 'WATER POLO', {
            fontSize: '64px',
            fontFamily: 'Arial',
            color: '#ffffff',
            fontStyle: 'bold',
            stroke: '#2C3E50',
            strokeThickness: 4
        }).setOrigin(0.5);
        
        const subtitle = this.add.text(width / 2, 180, 'CLASH', {
            fontSize: '48px',
            fontFamily: 'Arial',
            color: '#3498DB',
            fontStyle: 'bold',
            stroke: '#2C3E50',
            strokeThickness: 3
        }).setOrigin(0.5);
        
        // Animate title
        this.tweens.add({
            targets: [title, subtitle],
            scaleX: 1.05,
            scaleY: 1.05,
            duration: 2000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
    }
    
    createMenu() {
        const width = this.cameras.main.width;
        const height = this.cameras.main.height;
        
        const menuItems = [
            { text: 'SINGLE PLAYER', action: () => this.startSinglePlayer() },
            { text: 'ARCADE MODE', action: () => this.startArcadeMode() },
            { text: 'SETTINGS', action: () => this.showSettings() },
            { text: 'HOW TO PLAY', action: () => this.showInstructions() }
        ];
        
        this.menuButtons = [];
        
        menuItems.forEach((item, index) => {
            const y = height / 2 + index * 70;
            
            // Button background
            const buttonBg = this.add.image(width / 2, y, 'button');
            buttonBg.setInteractive();
            buttonBg.setScale(1.2, 1);
            
            // Button text
            const buttonText = this.add.text(width / 2, y, item.text, {
                fontSize: '20px',
                fontFamily: 'Arial',
                color: '#ffffff',
                fontStyle: 'bold'
            }).setOrigin(0.5);
            
            // Button interactions
            buttonBg.on('pointerover', () => {
                buttonBg.setTint(0xBBBBBB);
                buttonText.setScale(1.1);
            });
            
            buttonBg.on('pointerout', () => {
                buttonBg.clearTint();
                buttonText.setScale(1);
            });
            
            buttonBg.on('pointerdown', () => {
                buttonBg.setTint(0x888888);
                // Sound effect placeholder - would play button click sound
                // this.sound.play('button-click', { volume: 0.5 });
            });
            
            buttonBg.on('pointerup', () => {
                buttonBg.clearTint();
                item.action();
            });
            
            this.menuButtons.push({ bg: buttonBg, text: buttonText });
        });
        
        // Add keyboard navigation
        this.selectedIndex = 0;
        this.updateSelection();
        
        // Keyboard input
        this.cursors = this.input.keyboard.createCursorKeys();
        this.enterKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.ENTER);
        this.spaceKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.SPACE);
    }
    
    createInstructions() {
        const width = this.cameras.main.width;
        const height = this.cameras.main.height;
        
        const instructions = [
            'WASD - Move Player',
            'SPACE - Pass/Shoot',
            'TAB - Switch Player',
            'ESC - Pause Game'
        ];
        
        const instructionText = this.add.text(50, height - 120, instructions.join('\n'), {
            fontSize: '14px',
            fontFamily: 'Arial',
            color: '#ffffff',
            alpha: 0.8,
            lineSpacing: 5
        });
    }
    
    update() {
        // Handle keyboard navigation
        if (Phaser.Input.Keyboard.JustDown(this.cursors.up)) {
            this.selectedIndex = Math.max(0, this.selectedIndex - 1);
            this.updateSelection();
        } else if (Phaser.Input.Keyboard.JustDown(this.cursors.down)) {
            this.selectedIndex = Math.min(this.menuButtons.length - 1, this.selectedIndex + 1);
            this.updateSelection();
        }
        
        if (Phaser.Input.Keyboard.JustDown(this.enterKey) || 
            Phaser.Input.Keyboard.JustDown(this.spaceKey)) {
            this.selectCurrentItem();
        }
    }
    
    updateSelection() {
        this.menuButtons.forEach((button, index) => {
            if (index === this.selectedIndex) {
                button.bg.setTint(0xBBBBBB);
                button.text.setScale(1.1);
            } else {
                button.bg.clearTint();
                button.text.setScale(1);
            }
        });
    }
    
    selectCurrentItem() {
        switch (this.selectedIndex) {
            case 0:
                this.startSinglePlayer();
                break;
            case 1:
                this.startArcadeMode();
                break;
            case 2:
                this.showSettings();
                break;
            case 3:
                this.showInstructions();
                break;
        }
    }
    
    startSinglePlayer() {
        // Start single player game
        this.scene.start('GameScene', { 
            gameMode: 'singleplayer',
            arcadeMode: false 
        });
    }
    
    startArcadeMode() {
        // Start arcade mode
        this.scene.start('GameScene', { 
            gameMode: 'singleplayer',
            arcadeMode: true 
        });
    }
    
    showSettings() {
        // Create settings overlay
        this.createSettingsOverlay();
    }
    
    showInstructions() {
        // Create instructions overlay
        this.createInstructionsOverlay();
    }
    
    createSettingsOverlay() {
        const width = this.cameras.main.width;
        const height = this.cameras.main.height;
        
        // Overlay background
        const overlay = this.add.rectangle(width / 2, height / 2, width, height, 0x000000, 0.7);
        overlay.setInteractive();
        
        // Settings panel
        const panel = this.add.image(width / 2, height / 2, 'panel');
        panel.setScale(1.5);
        
        // Settings title
        const title = this.add.text(width / 2, height / 2 - 120, 'SETTINGS', {
            fontSize: '32px',
            fontFamily: 'Arial',
            color: '#ffffff',
            fontStyle: 'bold'
        }).setOrigin(0.5);
        
        // Settings options (placeholder)
        const settingsText = this.add.text(width / 2, height / 2, 'Settings coming soon!\n\nPress ESC to close', {
            fontSize: '18px',
            fontFamily: 'Arial',
            color: '#ffffff',
            align: 'center'
        }).setOrigin(0.5);
        
        // Close button
        const closeButton = this.add.text(width / 2, height / 2 + 80, 'CLOSE', {
            fontSize: '20px',
            fontFamily: 'Arial',
            color: '#3498DB',
            fontStyle: 'bold'
        }).setOrigin(0.5);
        
        closeButton.setInteractive();
        closeButton.on('pointerup', () => {
            overlay.destroy();
            panel.destroy();
            title.destroy();
            settingsText.destroy();
            closeButton.destroy();
        });
        
        // ESC key to close
        const escKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.ESC);
        escKey.once('down', () => {
            overlay.destroy();
            panel.destroy();
            title.destroy();
            settingsText.destroy();
            closeButton.destroy();
        });
    }
    
    createInstructionsOverlay() {
        const width = this.cameras.main.width;
        const height = this.cameras.main.height;
        
        // Overlay background
        const overlay = this.add.rectangle(width / 2, height / 2, width, height, 0x000000, 0.7);
        overlay.setInteractive();
        
        // Instructions panel
        const panel = this.add.image(width / 2, height / 2, 'panel');
        panel.setScale(2, 1.8);
        
        // Instructions title
        const title = this.add.text(width / 2, height / 2 - 140, 'HOW TO PLAY', {
            fontSize: '28px',
            fontFamily: 'Arial',
            color: '#ffffff',
            fontStyle: 'bold'
        }).setOrigin(0.5);
        
        // Instructions text
        const instructionsText = `CONTROLS:
WASD - Move your player
SPACE - Pass ball or shoot at goal
TAB - Switch to another player
ESC - Pause game

RULES:
• Score goals to win
• 28 second shot clock
• Fouls result in exclusions
• 4 quarters, 2 minutes each
• Goalkeepers can use hands in 6m area

Press ESC to close`;
        
        const instructions = this.add.text(width / 2, height / 2 - 20, instructionsText, {
            fontSize: '16px',
            fontFamily: 'Arial',
            color: '#ffffff',
            align: 'center',
            lineSpacing: 8
        }).setOrigin(0.5);
        
        // Close button
        const closeButton = this.add.text(width / 2, height / 2 + 120, 'CLOSE', {
            fontSize: '20px',
            fontFamily: 'Arial',
            color: '#3498DB',
            fontStyle: 'bold'
        }).setOrigin(0.5);
        
        closeButton.setInteractive();
        closeButton.on('pointerup', () => {
            overlay.destroy();
            panel.destroy();
            title.destroy();
            instructions.destroy();
            closeButton.destroy();
        });
        
        // ESC key to close
        const escKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.ESC);
        escKey.once('down', () => {
            overlay.destroy();
            panel.destroy();
            title.destroy();
            instructions.destroy();
            closeButton.destroy();
        });
    }
}
