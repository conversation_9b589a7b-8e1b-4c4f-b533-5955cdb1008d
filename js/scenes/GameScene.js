class GameScene extends Phaser.Scene {
    constructor() {
        super({ key: 'GameScene' });
    }
    
    init(data) {
        // Game mode settings
        this.gameMode = data.gameMode || 'singleplayer';
        this.arcadeMode = data.arcadeMode || false;
        
        // Game state
        this.gameState = GameStates.PLAYING;
        this.isPaused = false;
        
        // Teams and players
        this.teams = [];
        this.players = [];
        this.currentPlayer = null;
        this.ball = null;
        
        // Game timing
        this.gameTimer = GameConfig.RULES.QUARTER_DURATION;
        this.currentQuarter = 1;
        this.shotClock = GameConfig.RULES.SHOT_CLOCK_NORMAL;
        this.shotClockActive = false;
        
        // Score
        this.score = [0, 0]; // [team1, team2]
        
        // Game systems
        this.inputManager = null;
        this.gameRules = null;
        this.aiManager = null;
        this.hud = null;
        this.powerUpManager = null;
        
        // Pool boundaries
        this.poolBounds = {
            x: (GameConfig.CANVAS_WIDTH - GameConfig.POOL.WIDTH) / 2,
            y: (GameConfig.CANVAS_HEIGHT - GameConfig.POOL.HEIGHT) / 2,
            width: GameConfig.POOL.WIDTH,
            height: GameConfig.POOL.HEIGHT
        };
    }
    
    create() {
        // Create the pool environment
        this.createPool();
        
        // Initialize game systems
        this.initializeSystems();
        
        // Create teams and players
        this.createTeams();
        
        // Create the ball
        this.createBall();
        
        // Set up physics
        this.setupPhysics();
        
        // Create UI
        this.createUI();
        
        // Start the game
        this.startGame();
        
        console.log('Game Scene created successfully!');
    }
    
    createPool() {
        const bounds = this.poolBounds;
        
        // Pool background
        this.poolBackground = this.add.rectangle(
            bounds.x + bounds.width / 2,
            bounds.y + bounds.height / 2,
            bounds.width,
            bounds.height,
            GameConfig.POOL.WATER_COLOR
        );
        
        // Pool border
        this.poolBorder = this.add.graphics();
        this.poolBorder.lineStyle(4, 0xFFFFFF);
        this.poolBorder.strokeRect(bounds.x, bounds.y, bounds.width, bounds.height);
        
        // Goals
        this.createGoals();
        
        // Zone markings
        this.createZoneMarkings();
        
        // Lane markings
        this.createLaneMarkings();
    }
    
    createGoals() {
        const bounds = this.poolBounds;
        const goalWidth = GameConfig.POOL.GOAL_WIDTH;
        const goalHeight = GameConfig.POOL.GOAL_HEIGHT;
        
        // Left goal (Team 2 defends)
        this.leftGoal = this.add.rectangle(
            bounds.x,
            bounds.y + bounds.height / 2,
            goalHeight,
            goalWidth,
            GameConfig.POOL.GOAL_COLOR
        );
        
        // Right goal (Team 1 defends)
        this.rightGoal = this.add.rectangle(
            bounds.x + bounds.width,
            bounds.y + bounds.height / 2,
            goalHeight,
            goalWidth,
            GameConfig.POOL.GOAL_COLOR
        );
        
        // Goal posts
        this.add.rectangle(bounds.x, bounds.y + bounds.height / 2 - goalWidth / 2, 8, 8, 0xFFFFFF);
        this.add.rectangle(bounds.x, bounds.y + bounds.height / 2 + goalWidth / 2, 8, 8, 0xFFFFFF);
        this.add.rectangle(bounds.x + bounds.width, bounds.y + bounds.height / 2 - goalWidth / 2, 8, 8, 0xFFFFFF);
        this.add.rectangle(bounds.x + bounds.width, bounds.y + bounds.height / 2 + goalWidth / 2, 8, 8, 0xFFFFFF);
    }
    
    createZoneMarkings() {
        const bounds = this.poolBounds;
        const twoMeterLine = GameConfig.POOL.TWO_METER_LINE;
        const sixMeterLine = GameConfig.POOL.SIX_METER_LINE;
        
        const zoneGraphics = this.add.graphics();
        zoneGraphics.lineStyle(2, GameConfig.POOL.ZONE_COLOR, 0.8);
        
        // 2m lines
        zoneGraphics.beginPath();
        zoneGraphics.moveTo(bounds.x + twoMeterLine, bounds.y);
        zoneGraphics.lineTo(bounds.x + twoMeterLine, bounds.y + bounds.height);
        zoneGraphics.moveTo(bounds.x + bounds.width - twoMeterLine, bounds.y);
        zoneGraphics.lineTo(bounds.x + bounds.width - twoMeterLine, bounds.y + bounds.height);
        zoneGraphics.strokePath();
        
        // 6m lines
        zoneGraphics.lineStyle(3, GameConfig.POOL.ZONE_COLOR, 0.6);
        zoneGraphics.beginPath();
        zoneGraphics.moveTo(bounds.x + sixMeterLine, bounds.y);
        zoneGraphics.lineTo(bounds.x + sixMeterLine, bounds.y + bounds.height);
        zoneGraphics.moveTo(bounds.x + bounds.width - sixMeterLine, bounds.y);
        zoneGraphics.lineTo(bounds.x + bounds.width - sixMeterLine, bounds.y + bounds.height);
        zoneGraphics.strokePath();
        
        // Center line
        zoneGraphics.lineStyle(2, 0xFFFFFF, 0.5);
        zoneGraphics.beginPath();
        zoneGraphics.moveTo(bounds.x + bounds.width / 2, bounds.y);
        zoneGraphics.lineTo(bounds.x + bounds.width / 2, bounds.y + bounds.height);
        zoneGraphics.strokePath();
    }
    
    createLaneMarkings() {
        const bounds = this.poolBounds;
        const laneGraphics = this.add.graphics();
        laneGraphics.lineStyle(1, GameConfig.POOL.LANE_COLOR, 0.3);
        
        // Horizontal lane lines
        for (let i = 1; i < 7; i++) {
            const y = bounds.y + (bounds.height / 7) * i;
            laneGraphics.beginPath();
            laneGraphics.moveTo(bounds.x, y);
            laneGraphics.lineTo(bounds.x + bounds.width, y);
            laneGraphics.strokePath();
        }
    }
    
    initializeSystems() {
        // Input manager
        this.inputManager = new InputManager(this);

        // Game rules engine
        this.gameRules = new GameRules(this);

        // AI manager
        this.aiManager = new AIManager(this);

        // HUD
        this.hud = new HUD(this);

        // Power-up manager (only for arcade mode)
        if (this.arcadeMode) {
            this.powerUpManager = new PowerUpManager(this);
        }
    }
    
    createTeams() {
        // Team 1 (Player's team - Blue)
        const team1 = {
            id: Teams.TEAM_1,
            color: GameConfig.PLAYER.TEAM_1_COLOR,
            isPlayerTeam: true,
            players: []
        };
        
        // Team 2 (AI team - Red)
        const team2 = {
            id: Teams.TEAM_2,
            color: GameConfig.PLAYER.TEAM_2_COLOR,
            isPlayerTeam: false,
            players: []
        };
        
        this.teams = [team1, team2];
        
        // Create players for each team
        this.createPlayersForTeam(team1, true);
        this.createPlayersForTeam(team2, false);
        
        // Set initial player selection
        this.currentPlayer = team1.players[1]; // Start with center back
        this.currentPlayer.setSelected(true);
    }
    
    createPlayersForTeam(team, isPlayerTeam) {
        const bounds = this.poolBounds;
        const side = isPlayerTeam ? 1 : -1; // 1 for right side, -1 for left side
        
        // Starting positions for each player type
        const positions = [
            // Goalkeeper
            { 
                x: bounds.x + bounds.width / 2 + side * (bounds.width / 2 - 30),
                y: bounds.y + bounds.height / 2,
                position: PlayerPositions.GOALKEEPER
            },
            // Center Back
            { 
                x: bounds.x + bounds.width / 2 + side * (bounds.width / 4),
                y: bounds.y + bounds.height / 2,
                position: PlayerPositions.CENTER_BACK
            },
            // Left Back
            { 
                x: bounds.x + bounds.width / 2 + side * (bounds.width / 4),
                y: bounds.y + bounds.height / 2 - 80,
                position: PlayerPositions.LEFT_BACK
            },
            // Right Back
            { 
                x: bounds.x + bounds.width / 2 + side * (bounds.width / 4),
                y: bounds.y + bounds.height / 2 + 80,
                position: PlayerPositions.RIGHT_BACK
            },
            // Center Forward
            { 
                x: bounds.x + bounds.width / 2 - side * (bounds.width / 4),
                y: bounds.y + bounds.height / 2,
                position: PlayerPositions.CENTER_FORWARD
            },
            // Left Forward
            { 
                x: bounds.x + bounds.width / 2 - side * (bounds.width / 4),
                y: bounds.y + bounds.height / 2 - 60,
                position: PlayerPositions.LEFT_FORWARD
            },
            // Right Forward
            { 
                x: bounds.x + bounds.width / 2 - side * (bounds.width / 4),
                y: bounds.y + bounds.height / 2 + 60,
                position: PlayerPositions.RIGHT_FORWARD
            }
        ];
        
        positions.forEach((pos, index) => {
            const player = new Player(this, pos.x, pos.y, team.id, pos.position, index + 1);
            team.players.push(player);
            this.players.push(player);
        });
    }
    
    createBall() {
        const bounds = this.poolBounds;
        this.ball = new Ball(this, bounds.x + bounds.width / 2, bounds.y + bounds.height / 2);
    }
    
    setupPhysics() {
        // Set world bounds to pool area
        this.physics.world.setBounds(
            this.poolBounds.x,
            this.poolBounds.y,
            this.poolBounds.width,
            this.poolBounds.height
        );
        
        // Enable physics for all players
        this.players.forEach(player => {
            this.physics.add.existing(player);
            player.body.setCollideWorldBounds(true);
            player.body.setBounce(0.3);
        });
        
        // Enable physics for ball
        this.physics.add.existing(this.ball);
        this.ball.body.setCollideWorldBounds(true);
        this.ball.body.setBounce(GameConfig.BALL.BOUNCE);
        
        // Set up collisions
        this.setupCollisions();
    }
    
    setupCollisions() {
        // Player-player collisions
        this.physics.add.collider(this.players, this.players);
        
        // Player-ball collisions
        this.players.forEach(player => {
            this.physics.add.overlap(player, this.ball, (player, ball) => {
                this.handlePlayerBallCollision(player, ball);
            });
        });
    }
    
    handlePlayerBallCollision(player, ball) {
        // Handle ball pickup/interaction
        if (!ball.isHeld && player.canPickupBall()) {
            ball.setHolder(player);
        }
    }
    
    createUI() {
        this.hud.create();
    }
    
    startGame() {
        // Start game timers
        this.startGameTimer();
        this.startShotClock();
        
        // Initial ball possession
        this.gameRules.startPossession(Teams.TEAM_1);
        
        console.log('Game started!');
    }
    
    startGameTimer() {
        this.gameTimerEvent = this.time.addEvent({
            delay: 1000,
            callback: this.updateGameTimer,
            callbackScope: this,
            loop: true
        });
    }
    
    startShotClock() {
        this.shotClockEvent = this.time.addEvent({
            delay: 1000,
            callback: this.updateShotClock,
            callbackScope: this,
            loop: true
        });
    }
    
    updateGameTimer() {
        if (this.gameState === GameStates.PLAYING && !this.isPaused) {
            this.gameTimer--;
            
            if (this.gameTimer <= 0) {
                this.endQuarter();
            }
        }
    }
    
    updateShotClock() {
        if (this.gameState === GameStates.PLAYING && !this.isPaused && this.shotClockActive) {
            this.shotClock--;
            
            if (this.shotClock <= 0) {
                this.gameRules.shotClockViolation();
            }
        }
    }
    
    endQuarter() {
        this.currentQuarter++;
        
        if (this.currentQuarter > GameConfig.RULES.QUARTERS) {
            this.endGame();
        } else {
            this.gameState = GameStates.QUARTER_BREAK;
            this.gameTimer = GameConfig.RULES.QUARTER_DURATION;
            
            // Show quarter break screen
            this.showQuarterBreak();
        }
    }
    
    endGame() {
        this.gameState = GameStates.GAME_OVER;
        this.showGameOver();
    }
    
    showQuarterBreak() {
        // Implement quarter break screen
        console.log(`Quarter ${this.currentQuarter - 1} ended. Score: ${this.score[0]} - ${this.score[1]}`);
        
        // Auto-continue after 3 seconds
        this.time.delayedCall(3000, () => {
            this.gameState = GameStates.PLAYING;
        });
    }
    
    showGameOver() {
        console.log(`Game Over! Final Score: ${this.score[0]} - ${this.score[1]}`);
        
        // Return to main menu after 5 seconds
        this.time.delayedCall(5000, () => {
            this.scene.start('MainMenuScene');
        });
    }
    
    update(time, delta) {
        if (this.gameState !== GameStates.PLAYING || this.isPaused) {
            return;
        }
        
        // Update input
        this.inputManager.update();
        
        // Update players
        this.players.forEach(player => player.update(time, delta));
        
        // Update ball
        this.ball.update(time, delta);
        
        // Update AI
        this.aiManager.update(time, delta);

        // Update game rules
        this.gameRules.update(time, delta);

        // Update power-ups (arcade mode only)
        if (this.powerUpManager) {
            this.powerUpManager.update(time, delta);
        }

        // Update HUD
        this.hud.update();
    }
    
    pauseGame() {
        this.isPaused = true;
        this.gameState = GameStates.PAUSED;
        this.physics.pause();
    }
    
    resumeGame() {
        this.isPaused = false;
        this.gameState = GameStates.PLAYING;
        this.physics.resume();
    }
    
    // Public methods for game control
    switchPlayer() {
        if (this.currentPlayer) {
            this.currentPlayer.setSelected(false);
        }
        
        // Find next available player on player's team
        const playerTeam = this.teams[Teams.TEAM_1];
        const currentIndex = playerTeam.players.indexOf(this.currentPlayer);
        const nextIndex = (currentIndex + 1) % playerTeam.players.length;
        
        this.currentPlayer = playerTeam.players[nextIndex];
        this.currentPlayer.setSelected(true);
    }
    
    scoreGoal(team) {
        this.score[team]++;
        this.gameRules.resetAfterGoal();
        console.log(`Goal scored by team ${team + 1}! Score: ${this.score[0]} - ${this.score[1]}`);
    }
}
