class Ball extends Phaser.Physics.Arcade.Sprite {
    constructor(scene, x, y) {
        super(scene, x, y, 'ball');
        
        // Add to scene
        scene.add.existing(this);
        
        // Properties
        this.holder = null;
        this.isHeld = false;
        this.lastHolder = null;
        this.lastTouchTime = 0;
        
        // Physics properties
        this.maxSpeed = GameConfig.BALL.MAX_SPEED;
        this.waterResistance = GameConfig.BALL.WATER_RESISTANCE;
        
        // Goal detection
        this.lastPosition = { x: x, y: y };
        
        this.setupPhysics();
    }
    
    setupPhysics() {
        // Set physics properties
        this.setCollideWorldBounds(true);
        this.setBounce(GameConfig.BALL.BOUNCE);
        this.setDrag(GameConfig.BALL.DRAG * 100);
        this.setMaxVelocity(this.maxSpeed);
        
        // Set circular collision body
        this.body.setCircle(GameConfig.BALL.RADIUS);
        
        // Enable world bounds collision events
        this.body.onWorldBounds = true;
    }
    
    update(time, delta) {
        // Store last position for goal detection
        this.lastPosition.x = this.x;
        this.lastPosition.y = this.y;
        
        // Update position if held by player
        if (this.isHeld && this.holder) {
            this.updateHeldPosition();
        } else {
            // Apply water resistance
            this.applyWaterResistance();
            
            // Check for goal
            this.checkGoal();
            
            // Check for out of bounds
            this.checkOutOfBounds();
        }
        
        // Check for player pickup
        this.checkPlayerPickup();
    }
    
    updateHeldPosition() {
        if (!this.holder) return;
        
        // Position ball slightly in front of player
        const offset = GameConfig.PLAYER.RADIUS + GameConfig.BALL.RADIUS + 5;
        
        // Calculate position based on player's movement direction
        let offsetX = 0;
        let offsetY = 0;
        
        if (this.holder.body.velocity.x !== 0 || this.holder.body.velocity.y !== 0) {
            // Ball in front of movement direction
            const angle = Math.atan2(this.holder.body.velocity.y, this.holder.body.velocity.x);
            offsetX = Math.cos(angle) * offset;
            offsetY = Math.sin(angle) * offset;
        } else {
            // Default position (slightly to the right)
            offsetX = offset;
        }
        
        this.setPosition(
            this.holder.x + offsetX,
            this.holder.y + offsetY
        );
        
        // Stop any physics movement
        this.body.setVelocity(0, 0);
    }
    
    applyWaterResistance() {
        // Apply stronger resistance than players due to ball's properties
        if (this.body.velocity.x !== 0 || this.body.velocity.y !== 0) {
            this.body.velocity.x *= this.waterResistance;
            this.body.velocity.y *= this.waterResistance;
            
            // Stop very slow movement
            if (Math.abs(this.body.velocity.x) < 1) {
                this.body.velocity.x = 0;
            }
            if (Math.abs(this.body.velocity.y) < 1) {
                this.body.velocity.y = 0;
            }
        }
    }
    
    checkGoal() {
        const bounds = this.scene.poolBounds;
        const goalWidth = GameConfig.POOL.GOAL_WIDTH;
        const goalY = bounds.y + bounds.height / 2;
        
        // Check left goal (Team 1 scores)
        if (this.x <= bounds.x && 
            this.y >= goalY - goalWidth / 2 && 
            this.y <= goalY + goalWidth / 2) {
            this.handleGoal(Teams.TEAM_1);
            return;
        }
        
        // Check right goal (Team 2 scores)
        if (this.x >= bounds.x + bounds.width && 
            this.y >= goalY - goalWidth / 2 && 
            this.y <= goalY + goalWidth / 2) {
            this.handleGoal(Teams.TEAM_2);
            return;
        }
    }
    
    handleGoal(scoringTeam) {
        console.log(`Goal scored by team ${scoringTeam + 1}!`);
        
        // Stop ball movement
        this.body.setVelocity(0, 0);
        
        // Notify game scene
        this.scene.scoreGoal(scoringTeam);
        
        // Reset ball to center
        this.resetToCenter();
    }
    
    checkOutOfBounds() {
        const bounds = this.scene.poolBounds;
        
        // Check if ball went out of bounds (not through goal)
        if (this.x < bounds.x - 20 || this.x > bounds.x + bounds.width + 20 ||
            this.y < bounds.y - 20 || this.y > bounds.y + bounds.height + 20) {
            
            // Determine which team gets possession
            const possessionTeam = this.lastHolder ? 
                (this.lastHolder.team === Teams.TEAM_1 ? Teams.TEAM_2 : Teams.TEAM_1) : 
                Teams.TEAM_1;
            
            this.handleOutOfBounds(possessionTeam);
        }
    }
    
    handleOutOfBounds(possessionTeam) {
        console.log(`Ball out of bounds. Possession to team ${possessionTeam + 1}`);
        
        // Reset ball position
        this.resetToSideline();
        
        // Give possession to appropriate team
        this.scene.gameRules.changePossession(possessionTeam);
    }
    
    checkPlayerPickup() {
        if (this.isHeld) return;
        
        // Check distance to all players
        this.scene.players.forEach(player => {
            if (player.canPickupBall()) {
                const distance = GameUtils.getDistance(this.x, this.y, player.x, player.y);
                const pickupDistance = GameConfig.PLAYER.RADIUS + GameConfig.BALL.RADIUS + 5;
                
                if (distance <= pickupDistance) {
                    this.attemptPickup(player);
                }
            }
        });
    }
    
    attemptPickup(player) {
        // Check if player can actually pick up the ball
        if (!player.canPickupBall() || this.isHeld) return false;
        
        // Successful pickup
        this.setHolder(player);
        player.pickupBall(this);
        
        this.lastTouchTime = this.scene.time.now;
        
        return true;
    }
    
    setHolder(player) {
        this.holder = player;
        this.isHeld = true;
        this.lastHolder = player;
        
        // Stop physics movement
        this.body.setVelocity(0, 0);
        
        console.log(`Player ${player.number} picked up the ball`);
    }
    
    release() {
        if (!this.isHeld) return;
        
        console.log(`Ball released by player ${this.holder ? this.holder.number : 'unknown'}`);
        
        this.holder = null;
        this.isHeld = false;
    }
    
    resetToCenter() {
        const bounds = this.scene.poolBounds;
        this.setPosition(
            bounds.x + bounds.width / 2,
            bounds.y + bounds.height / 2
        );
        this.body.setVelocity(0, 0);
        this.release();
    }
    
    resetToSideline() {
        const bounds = this.scene.poolBounds;
        
        // Place ball at nearest sideline position
        let x, y;
        
        if (this.x < bounds.x + bounds.width / 2) {
            // Left side
            x = bounds.x + 50;
        } else {
            // Right side
            x = bounds.x + bounds.width - 50;
        }
        
        // Keep Y position but clamp to bounds
        y = GameUtils.clamp(this.y, bounds.y + 50, bounds.y + bounds.height - 50);
        
        this.setPosition(x, y);
        this.body.setVelocity(0, 0);
        this.release();
    }
    
    // Utility methods
    getSpeed() {
        return Math.sqrt(
            this.body.velocity.x * this.body.velocity.x + 
            this.body.velocity.y * this.body.velocity.y
        );
    }
    
    getDirection() {
        return Math.atan2(this.body.velocity.y, this.body.velocity.x);
    }
    
    isMoving() {
        return this.getSpeed() > 5; // Threshold for "moving"
    }
    
    isNearGoal(team) {
        const bounds = this.scene.poolBounds;
        const goalThreshold = 100; // Distance from goal line
        
        if (team === Teams.TEAM_1) {
            // Team 1 attacks right goal
            return this.x > bounds.x + bounds.width - goalThreshold;
        } else {
            // Team 2 attacks left goal
            return this.x < bounds.x + goalThreshold;
        }
    }
    
    getDistanceToGoal(team) {
        const bounds = this.scene.poolBounds;
        
        if (team === Teams.TEAM_1) {
            // Distance to right goal
            return GameUtils.getDistance(
                this.x, this.y,
                bounds.x + bounds.width, bounds.y + bounds.height / 2
            );
        } else {
            // Distance to left goal
            return GameUtils.getDistance(
                this.x, this.y,
                bounds.x, bounds.y + bounds.height / 2
            );
        }
    }
    
    getAngleToGoal(team) {
        const bounds = this.scene.poolBounds;
        
        if (team === Teams.TEAM_1) {
            // Angle to right goal
            return GameUtils.getAngle(
                this.x, this.y,
                bounds.x + bounds.width, bounds.y + bounds.height / 2
            );
        } else {
            // Angle to left goal
            return GameUtils.getAngle(
                this.x, this.y,
                bounds.x, bounds.y + bounds.height / 2
            );
        }
    }
    
    // Force methods for external control
    applyForce(forceX, forceY) {
        if (this.isHeld) return;
        
        this.body.setVelocity(
            this.body.velocity.x + forceX,
            this.body.velocity.y + forceY
        );
    }
    
    setVelocityTowards(targetX, targetY, speed) {
        if (this.isHeld) return;
        
        const angle = GameUtils.getAngle(this.x, this.y, targetX, targetY);
        this.body.setVelocity(
            Math.cos(angle) * speed,
            Math.sin(angle) * speed
        );
    }
}
