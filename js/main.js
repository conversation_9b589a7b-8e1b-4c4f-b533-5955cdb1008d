// Main game initialization
class WaterPoloGame {
    constructor() {
        this.config = {
            type: Phaser.AUTO,
            width: GameConfig.CANVAS_WIDTH,
            height: GameConfig.CANVAS_HEIGHT,
            canvas: document.getElementById('game-canvas'),
            physics: {
                default: 'arcade',
                arcade: {
                    gravity: { y: 0 },
                    debug: GameConfig.PERFORMANCE.DEBUG_MODE
                }
            },
            scene: [PreloadScene, MainMenuScene, GameScene],
            scale: {
                mode: Phaser.Scale.FIT,
                autoCenter: Phaser.Scale.CENTER_BOTH,
                min: {
                    width: 320,
                    height: 200
                },
                max: {
                    width: 1920,
                    height: 1200
                }
            },
            render: {
                antialias: !this.isMobile,
                pixelArt: false,
                roundPixels: true
            },
            backgroundColor: GameConfig.POOL.WATER_COLOR,
            antialias: true,
            pixelArt: false
        };
        
        this.game = null;
        this.isInitialized = false;
    }
    
    init() {
        if (this.isInitialized) {
            return;
        }
        
        // Hide loading screen
        const loading = document.getElementById('loading');
        if (loading) {
            loading.style.display = 'none';
        }
        
        // Initialize Phaser game
        this.game = new Phaser.Game(this.config);
        
        // Set up global game reference
        window.waterPoloGame = this;
        
        // Set up mobile detection
        this.isMobile = this.detectMobile();
        
        // Set up touch controls if mobile
        if (this.isMobile) {
            this.setupTouchControls();
        }
        
        this.isInitialized = true;
        
        console.log('Water Polo Clash initialized successfully!');
    }
    
    detectMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               (navigator.maxTouchPoints && navigator.maxTouchPoints > 2);
    }
    
    setupTouchControls() {
        const touchControls = document.querySelector('.touch-controls');
        if (touchControls) {
            touchControls.style.display = 'flex';
        }
        
        // Set up movement pad
        this.setupMovementPad();
        
        // Set up action buttons
        this.setupActionButtons();
    }
    
    setupMovementPad() {
        const movementPad = document.getElementById('movement-pad');
        if (!movementPad) return;
        
        let isPressed = false;
        let startX = 0;
        let startY = 0;
        
        const handleStart = (e) => {
            e.preventDefault();
            isPressed = true;
            const rect = movementPad.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            
            startX = centerX;
            startY = centerY;
            
            movementPad.style.background = 'rgba(255, 255, 255, 0.4)';
        };
        
        const handleMove = (e) => {
            if (!isPressed) return;
            e.preventDefault();
            
            const touch = e.touches ? e.touches[0] : e;
            const deltaX = touch.clientX - startX;
            const deltaY = touch.clientY - startY;
            
            // Normalize and send to game
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
            const maxDistance = GameConfig.INPUT.TOUCH_MAX_DISTANCE;
            
            if (distance > GameConfig.INPUT.TOUCH_DEADZONE) {
                const normalizedX = Math.max(-1, Math.min(1, deltaX / maxDistance));
                const normalizedY = Math.max(-1, Math.min(1, deltaY / maxDistance));
                
                // Send input to current scene
                if (this.game && this.game.scene.isActive('GameScene')) {
                    const gameScene = this.game.scene.getScene('GameScene');
                    if (gameScene && gameScene.inputManager) {
                        gameScene.inputManager.setTouchMovement(normalizedX, normalizedY);
                    }
                }
            }
        };
        
        const handleEnd = (e) => {
            e.preventDefault();
            isPressed = false;
            movementPad.style.background = 'rgba(255, 255, 255, 0.2)';
            
            // Stop movement
            if (this.game && this.game.scene.isActive('GameScene')) {
                const gameScene = this.game.scene.getScene('GameScene');
                if (gameScene && gameScene.inputManager) {
                    gameScene.inputManager.setTouchMovement(0, 0);
                }
            }
        };
        
        // Touch events
        movementPad.addEventListener('touchstart', handleStart, { passive: false });
        movementPad.addEventListener('touchmove', handleMove, { passive: false });
        movementPad.addEventListener('touchend', handleEnd, { passive: false });
        
        // Mouse events for testing on desktop
        movementPad.addEventListener('mousedown', handleStart);
        movementPad.addEventListener('mousemove', handleMove);
        movementPad.addEventListener('mouseup', handleEnd);
        movementPad.addEventListener('mouseleave', handleEnd);
    }
    
    setupActionButtons() {
        const buttons = {
            'pass-btn': 'pass',
            'shoot-btn': 'shoot',
            'switch-btn': 'switch'
        };
        
        Object.entries(buttons).forEach(([id, action]) => {
            const button = document.getElementById(id);
            if (!button) return;
            
            const handleAction = (e) => {
                e.preventDefault();
                button.style.background = 'rgba(255, 255, 255, 0.5)';
                
                // Send action to game
                if (this.game && this.game.scene.isActive('GameScene')) {
                    const gameScene = this.game.scene.getScene('GameScene');
                    if (gameScene && gameScene.inputManager) {
                        gameScene.inputManager.triggerTouchAction(action);
                    }
                }
                
                // Reset button appearance
                setTimeout(() => {
                    button.style.background = 'rgba(255, 255, 255, 0.2)';
                }, 150);
            };
            
            button.addEventListener('touchstart', handleAction, { passive: false });
            button.addEventListener('mousedown', handleAction);
        });
    }
    
    // Public methods for game control
    pauseGame() {
        if (this.game && this.game.scene.isActive('GameScene')) {
            const gameScene = this.game.scene.getScene('GameScene');
            if (gameScene) {
                gameScene.pauseGame();
            }
        }
    }
    
    resumeGame() {
        if (this.game && this.game.scene.isActive('GameScene')) {
            const gameScene = this.game.scene.getScene('GameScene');
            if (gameScene) {
                gameScene.resumeGame();
            }
        }
    }
    
    restartGame() {
        if (this.game) {
            this.game.scene.start('MainMenuScene');
        }
    }
}

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    const game = new WaterPoloGame();
    
    // Small delay to ensure all resources are loaded
    setTimeout(() => {
        game.init();
    }, 100);
});

// Handle page visibility changes
document.addEventListener('visibilitychange', () => {
    if (window.waterPoloGame && window.waterPoloGame.game) {
        if (document.hidden) {
            window.waterPoloGame.pauseGame();
        } else {
            // Don't auto-resume, let player choose
        }
    }
});

// Handle window resize
window.addEventListener('resize', () => {
    if (window.waterPoloGame && window.waterPoloGame.game) {
        window.waterPoloGame.game.scale.refresh();
    }
});
