class AIManager {
    constructor(scene) {
        this.scene = scene;
        
        // AI settings
        this.skillLevel = GameConfig.AI.SKILL_LEVEL;
        this.reactionTime = GameConfig.AI.REACTION_TIME;
        this.updateRate = GameConfig.AI.POSITIONING_UPDATE_RATE;
        
        // AI state
        this.lastUpdate = 0;
        this.teamStrategies = {
            [Teams.TEAM_1]: 'balanced',
            [Teams.TEAM_2]: 'balanced'
        };
        
        // Player AI states
        this.playerStates = new Map();
        
        this.initializePlayerStates();
    }
    
    initializePlayerStates() {
        this.scene.players.forEach(player => {
            this.playerStates.set(player, {
                state: 'positioning',
                target: null,
                lastDecision: 0,
                priority: 0
            });
        });
    }
    
    update(time, delta) {
        // Update AI at specified rate
        if (time - this.lastUpdate < this.updateRate * 1000) {
            return;
        }
        
        this.lastUpdate = time;
        
        // Update team strategies
        this.updateTeamStrategies();
        
        // Update individual player AI
        this.updatePlayerAI(time, delta);
    }
    
    updateTeamStrategies() {
        // Analyze game state and adjust strategies
        const ball = this.scene.ball;
        const gameState = this.analyzeGameState();
        
        this.scene.teams.forEach((team, teamIndex) => {
            if (team.isPlayerTeam) return; // Skip player-controlled team
            
            let strategy = 'balanced';
            
            // Determine strategy based on game state
            if (gameState.possession === teamIndex) {
                if (gameState.timeLeft < 30) {
                    strategy = 'aggressive';
                } else if (gameState.scoreDifference > 0) {
                    strategy = 'possession';
                } else {
                    strategy = 'attacking';
                }
            } else {
                if (gameState.scoreDifference < -1) {
                    strategy = 'pressing';
                } else {
                    strategy = 'defensive';
                }
            }
            
            this.teamStrategies[teamIndex] = strategy;
        });
    }
    
    analyzeGameState() {
        return {
            possession: this.scene.gameRules.currentPossession,
            timeLeft: this.scene.gameTimer,
            quarter: this.scene.currentQuarter,
            scoreDifference: this.scene.score[1] - this.scene.score[0], // AI team perspective
            shotClock: this.scene.shotClock,
            ballPosition: this.scene.ball ? { x: this.scene.ball.x, y: this.scene.ball.y } : null
        };
    }
    
    updatePlayerAI(time, delta) {
        this.scene.players.forEach(player => {
            if (player.team === Teams.TEAM_1) return; // Skip player team
            if (player.isExcluded) return; // Skip excluded players
            
            this.updateIndividualPlayerAI(player, time, delta);
        });
    }
    
    updateIndividualPlayerAI(player, time, delta) {
        const playerState = this.playerStates.get(player);
        const ball = this.scene.ball;
        
        // Add reaction time delay
        if (time - playerState.lastDecision < this.reactionTime * 1000) {
            return;
        }
        
        // Determine player's role and behavior
        if (player.isGoalkeeper()) {
            this.updateGoalkeeperAI(player, playerState);
        } else {
            this.updateFieldPlayerAI(player, playerState);
        }
        
        playerState.lastDecision = time;
    }
    
    updateGoalkeeperAI(player, playerState) {
        const ball = this.scene.ball;
        const bounds = this.scene.poolBounds;
        
        if (!ball) return;
        
        // Goalkeeper behavior
        if (ball.holder && ball.holder.team !== player.team) {
            // Position to defend goal
            const goalX = player.team === Teams.TEAM_1 ? 
                bounds.x + bounds.width - 20 : bounds.x + 20;
            
            // Calculate optimal position based on ball position
            const ballAngle = GameUtils.getAngle(goalX, bounds.y + bounds.height / 2, ball.x, ball.y);
            const distance = 30; // Distance from goal line
            
            const targetX = goalX + Math.cos(ballAngle) * distance;
            const targetY = bounds.y + bounds.height / 2 + Math.sin(ballAngle) * distance;
            
            player.moveTowards(targetX, targetY);
            playerState.state = 'defending';
        } else if (!ball.isHeld && player.isInGoalArea()) {
            // Try to get loose ball in goal area
            const distance = player.getDistanceTo(ball.x, ball.y);
            if (distance < 80) {
                player.moveTowards(ball.x, ball.y);
                playerState.state = 'collecting';
            }
        } else if (player.hasBall) {
            // Goalkeeper has ball - make a pass
            this.makeGoalkeeperPass(player);
            playerState.state = 'passing';
        } else {
            // Default positioning
            const goalX = player.team === Teams.TEAM_1 ? 
                bounds.x + bounds.width - 25 : bounds.x + 25;
            player.moveTowards(goalX, bounds.y + bounds.height / 2);
            playerState.state = 'positioning';
        }
    }
    
    makeGoalkeeperPass(player) {
        // Find best teammate for pass
        const team = this.scene.teams[player.team];
        let bestTarget = null;
        let bestScore = -1;
        
        team.players.forEach(teammate => {
            if (teammate === player || teammate.isExcluded) return;
            
            const distance = player.getDistanceTo(teammate.x, teammate.y);
            const isOpen = this.isPlayerOpen(teammate);
            
            let score = 100 - distance * 0.3;
            if (isOpen) score += 50;
            
            // Prefer forwards
            if (teammate.position >= PlayerPositions.CENTER_FORWARD) {
                score += 30;
            }
            
            if (score > bestScore) {
                bestScore = score;
                bestTarget = teammate;
            }
        });
        
        if (bestTarget) {
            player.passBall(bestTarget.x, bestTarget.y);
        }
    }
    
    updateFieldPlayerAI(player, playerState) {
        const ball = this.scene.ball;
        const strategy = this.teamStrategies[player.team];
        
        if (!ball) return;
        
        if (player.hasBall) {
            this.handlePlayerWithBall(player, playerState, strategy);
        } else {
            this.handlePlayerWithoutBall(player, playerState, strategy);
        }
    }
    
    handlePlayerWithBall(player, playerState, strategy) {
        const bounds = this.scene.poolBounds;
        const goalDistance = this.scene.ball.getDistanceToGoal(player.team);
        
        // Decide action based on position and strategy
        if (goalDistance < 100 && this.hasShootingAngle(player)) {
            // Shoot at goal
            const goalX = player.team === Teams.TEAM_1 ? 
                bounds.x + bounds.width : bounds.x;
            const goalY = bounds.y + bounds.height / 2 + (Math.random() - 0.5) * 60;
            
            player.shootBall(goalX, goalY);
            playerState.state = 'shooting';
        } else {
            // Look for pass
            const passTarget = this.findBestPassTarget(player, strategy);
            if (passTarget) {
                player.passBall(passTarget.x, passTarget.y);
                playerState.state = 'passing';
            } else {
                // Move forward with ball
                this.moveForwardWithBall(player, strategy);
                playerState.state = 'advancing';
            }
        }
    }
    
    handlePlayerWithoutBall(player, playerState, strategy) {
        const ball = this.scene.ball;
        
        if (!ball.isHeld) {
            // Try to get loose ball
            const distance = player.getDistanceTo(ball.x, ball.y);
            if (distance < 60) {
                player.moveTowards(ball.x, ball.y);
                playerState.state = 'chasing_ball';
                return;
            }
        }
        
        if (ball.holder && ball.holder.team === player.team) {
            // Support teammate with ball
            this.supportTeammate(player, ball.holder, strategy);
            playerState.state = 'supporting';
        } else if (ball.holder && ball.holder.team !== player.team) {
            // Defend against opponent
            this.defendAgainstOpponent(player, ball.holder, strategy);
            playerState.state = 'defending';
        } else {
            // Position according to strategy
            this.positionPlayer(player, strategy);
            playerState.state = 'positioning';
        }
    }
    
    hasShootingAngle(player) {
        const bounds = this.scene.poolBounds;
        const goalX = player.team === Teams.TEAM_1 ? 
            bounds.x + bounds.width : bounds.x;
        const goalY = bounds.y + bounds.height / 2;
        
        // Simple check - could be improved with line-of-sight
        const angle = Math.abs(GameUtils.getAngle(player.x, player.y, goalX, goalY));
        return angle < Math.PI / 3; // 60 degree shooting angle
    }
    
    findBestPassTarget(player, strategy) {
        const team = this.scene.teams[player.team];
        let bestTarget = null;
        let bestScore = -1;
        
        team.players.forEach(teammate => {
            if (teammate === player || teammate.isExcluded) return;
            
            const distance = player.getDistanceTo(teammate.x, teammate.y);
            const isOpen = this.isPlayerOpen(teammate);
            const goalDistance = teammate.getDistanceTo(
                player.team === Teams.TEAM_1 ? 
                    this.scene.poolBounds.x + this.scene.poolBounds.width : 
                    this.scene.poolBounds.x,
                this.scene.poolBounds.y + this.scene.poolBounds.height / 2
            );
            
            let score = 100 - distance * 0.5;
            if (isOpen) score += 40;
            
            // Strategy-based scoring
            switch (strategy) {
                case 'attacking':
                    score += (200 - goalDistance) * 0.3;
                    break;
                case 'possession':
                    if (isOpen) score += 30;
                    break;
                case 'aggressive':
                    if (teammate.position >= PlayerPositions.CENTER_FORWARD) {
                        score += 50;
                    }
                    break;
            }
            
            if (score > bestScore) {
                bestScore = score;
                bestTarget = teammate;
            }
        });
        
        return bestTarget;
    }
    
    isPlayerOpen(player) {
        // Check if player is relatively free from opponents
        const opponents = this.scene.teams[player.team === Teams.TEAM_1 ? Teams.TEAM_2 : Teams.TEAM_1].players;
        
        for (let opponent of opponents) {
            if (opponent.isExcluded) continue;
            
            const distance = player.getDistanceTo(opponent.x, opponent.y);
            if (distance < 40) {
                return false; // Too close to opponent
            }
        }
        
        return true;
    }
    
    moveForwardWithBall(player, strategy) {
        const bounds = this.scene.poolBounds;
        const targetX = player.team === Teams.TEAM_1 ? 
            bounds.x + bounds.width * 0.8 : bounds.x + bounds.width * 0.2;
        
        player.moveTowards(targetX, player.y);
    }
    
    supportTeammate(player, teammate, strategy) {
        // Position to receive pass
        const bounds = this.scene.poolBounds;
        const goalX = player.team === Teams.TEAM_1 ? 
            bounds.x + bounds.width : bounds.x;
        
        // Position between teammate and goal
        const supportX = teammate.x + (goalX - teammate.x) * 0.3;
        const supportY = teammate.y + (Math.random() - 0.5) * 80;
        
        player.moveTowards(supportX, supportY);
    }
    
    defendAgainstOpponent(player, opponent, strategy) {
        // Position between opponent and goal
        const bounds = this.scene.poolBounds;
        const goalX = player.team === Teams.TEAM_1 ? 
            bounds.x : bounds.x + bounds.width;
        const goalY = bounds.y + bounds.height / 2;
        
        // Calculate defensive position
        const defenseX = opponent.x + (goalX - opponent.x) * 0.3;
        const defenseY = opponent.y + (goalY - opponent.y) * 0.2;
        
        player.moveTowards(defenseX, defenseY);
    }
    
    positionPlayer(player, strategy) {
        // Default positioning based on player role and strategy
        const bounds = this.scene.poolBounds;
        const side = player.team === Teams.TEAM_1 ? 1 : -1;
        
        let targetX, targetY;
        
        switch (player.position) {
            case PlayerPositions.CENTER_BACK:
                targetX = bounds.x + bounds.width / 2 + side * bounds.width * 0.2;
                targetY = bounds.y + bounds.height / 2;
                break;
            case PlayerPositions.LEFT_BACK:
                targetX = bounds.x + bounds.width / 2 + side * bounds.width * 0.2;
                targetY = bounds.y + bounds.height * 0.3;
                break;
            case PlayerPositions.RIGHT_BACK:
                targetX = bounds.x + bounds.width / 2 + side * bounds.width * 0.2;
                targetY = bounds.y + bounds.height * 0.7;
                break;
            default:
                // Forwards
                targetX = bounds.x + bounds.width / 2 - side * bounds.width * 0.2;
                targetY = bounds.y + bounds.height / 2 + (player.position - 4) * 60;
                break;
        }
        
        player.moveTowards(targetX, targetY);
    }
    
    // Utility methods
    getPlayerState(player) {
        return this.playerStates.get(player);
    }
    
    setPlayerState(player, state) {
        const playerState = this.playerStates.get(player);
        if (playerState) {
            playerState.state = state;
        }
    }
    
    getTeamStrategy(team) {
        return this.teamStrategies[team];
    }
    
    setTeamStrategy(team, strategy) {
        this.teamStrategies[team] = strategy;
    }
}
