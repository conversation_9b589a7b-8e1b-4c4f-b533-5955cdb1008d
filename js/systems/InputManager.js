class InputManager {
    constructor(scene) {
        this.scene = scene;
        
        // Input states
        this.keys = {};
        this.touchInput = {
            movementX: 0,
            movementY: 0,
            isActive: false
        };
        
        // Action states
        this.actionPressed = false;
        this.lastActionTime = 0;
        this.actionCooldown = 200; // milliseconds
        
        this.setupKeyboardInput();
        this.setupMouseInput();
    }
    
    setupKeyboardInput() {
        // Movement keys
        this.keys.W = this.scene.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.W);
        this.keys.A = this.scene.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.A);
        this.keys.S = this.scene.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.S);
        this.keys.D = this.scene.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.D);
        
        // Action keys
        this.keys.SPACE = this.scene.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.SPACE);
        this.keys.TAB = this.scene.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.TAB);
        this.keys.ESC = this.scene.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.ESC);
        
        // Alternative keys
        this.keys.UP = this.scene.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.UP);
        this.keys.DOWN = this.scene.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.DOWN);
        this.keys.LEFT = this.scene.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.LEFT);
        this.keys.RIGHT = this.scene.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.RIGHT);
        this.keys.ENTER = this.scene.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.ENTER);
        
        // Prevent default behavior for TAB
        this.keys.TAB.preventDefault = true;
    }
    
    setupMouseInput() {
        // Mouse/touch input for actions
        this.scene.input.on('pointerdown', (pointer) => {
            this.handlePointerAction(pointer);
        });
        
        // Right click for alternative actions
        this.scene.input.on('pointerdown', (pointer) => {
            if (pointer.rightButtonDown()) {
                this.handleRightClick(pointer);
            }
        });
    }
    
    update() {
        // Handle movement input
        this.handleMovementInput();
        
        // Handle action input
        this.handleActionInput();
        
        // Handle system input
        this.handleSystemInput();
    }
    
    handleMovementInput() {
        if (!this.scene.currentPlayer || this.scene.isPaused) return;
        
        let moveX = 0;
        let moveY = 0;
        
        // Keyboard input
        if (GameConfig.INPUT.KEYBOARD_ENABLED) {
            if (this.keys.W.isDown || this.keys.UP.isDown) moveY -= 1;
            if (this.keys.S.isDown || this.keys.DOWN.isDown) moveY += 1;
            if (this.keys.A.isDown || this.keys.LEFT.isDown) moveX -= 1;
            if (this.keys.D.isDown || this.keys.RIGHT.isDown) moveX += 1;
        }
        
        // Touch input
        if (GameConfig.INPUT.TOUCH_ENABLED && this.touchInput.isActive) {
            moveX += this.touchInput.movementX;
            moveY += this.touchInput.movementY;
        }
        
        // Apply movement
        if (moveX !== 0 || moveY !== 0) {
            this.scene.currentPlayer.moveInDirection(moveX, moveY);
        } else {
            this.scene.currentPlayer.stopMovement();
        }
    }
    
    handleActionInput() {
        if (!this.scene.currentPlayer || this.scene.isPaused) return;
        
        const currentTime = this.scene.time.now;
        
        // Check for action input
        let actionTriggered = false;
        
        if (Phaser.Input.Keyboard.JustDown(this.keys.SPACE) || 
            Phaser.Input.Keyboard.JustDown(this.keys.ENTER)) {
            actionTriggered = true;
        }
        
        // Apply action cooldown
        if (actionTriggered && currentTime - this.lastActionTime > this.actionCooldown) {
            this.performAction();
            this.lastActionTime = currentTime;
        }
    }
    
    handleSystemInput() {
        // Player switching
        if (Phaser.Input.Keyboard.JustDown(this.keys.TAB)) {
            this.scene.switchPlayer();
        }
        
        // Pause game
        if (Phaser.Input.Keyboard.JustDown(this.keys.ESC)) {
            if (this.scene.isPaused) {
                this.scene.resumeGame();
            } else {
                this.scene.pauseGame();
            }
        }
    }
    
    performAction() {
        const player = this.scene.currentPlayer;
        if (!player) return;
        
        if (player.hasBall) {
            // Determine action based on context
            if (this.shouldShoot()) {
                this.performShoot();
            } else {
                this.performPass();
            }
        } else {
            // Try to get the ball or perform defensive action
            this.performDefensiveAction();
        }
    }
    
    shouldShoot() {
        const player = this.scene.currentPlayer;
        const ball = this.scene.ball;
        
        if (!player || !ball || !player.hasBall) return false;
        
        // Check if player is in shooting position
        const distanceToGoal = ball.getDistanceToGoal(player.team);
        const shootingRange = 150; // pixels
        
        return distanceToGoal < shootingRange;
    }
    
    performShoot() {
        const player = this.scene.currentPlayer;
        const bounds = this.scene.poolBounds;
        
        // Determine target goal
        let targetX, targetY;
        
        if (player.team === Teams.TEAM_1) {
            // Shoot at right goal
            targetX = bounds.x + bounds.width;
            targetY = bounds.y + bounds.height / 2;
        } else {
            // Shoot at left goal
            targetX = bounds.x;
            targetY = bounds.y + bounds.height / 2;
        }
        
        // Add some randomness to make it more realistic
        targetY += (Math.random() - 0.5) * 60;
        
        player.shootBall(targetX, targetY);
        console.log(`Player ${player.number} shoots!`);
    }
    
    performPass() {
        const player = this.scene.currentPlayer;
        
        // Find best teammate to pass to
        const target = this.findBestPassTarget();
        
        if (target) {
            player.passBall(target.x, target.y);
            console.log(`Player ${player.number} passes to player ${target.number}`);
        } else {
            // No good pass target, try a forward pass
            this.performForwardPass();
        }
    }
    
    findBestPassTarget() {
        const player = this.scene.currentPlayer;
        const team = this.scene.teams[player.team];
        
        let bestTarget = null;
        let bestScore = -1;
        
        team.players.forEach(teammate => {
            if (teammate === player || teammate.isExcluded) return;
            
            const distance = player.getDistanceTo(teammate.x, teammate.y);
            const angle = player.getAngleTo(teammate.x, teammate.y);
            
            // Score based on distance and position
            let score = 100 - distance * 0.5;
            
            // Prefer forward passes
            const forwardDirection = player.team === Teams.TEAM_1 ? 0 : Math.PI;
            const angleDiff = Math.abs(angle - forwardDirection);
            score += (Math.PI - angleDiff) * 20;
            
            // Prefer players closer to goal
            const goalDistance = this.scene.ball.getDistanceToGoal(player.team);
            const teammateGoalDistance = GameUtils.getDistance(
                teammate.x, teammate.y,
                player.team === Teams.TEAM_1 ? 
                    this.scene.poolBounds.x + this.scene.poolBounds.width : 
                    this.scene.poolBounds.x,
                this.scene.poolBounds.y + this.scene.poolBounds.height / 2
            );
            
            if (teammateGoalDistance < goalDistance) {
                score += 30;
            }
            
            if (score > bestScore) {
                bestScore = score;
                bestTarget = teammate;
            }
        });
        
        return bestTarget;
    }
    
    performForwardPass() {
        const player = this.scene.currentPlayer;
        const bounds = this.scene.poolBounds;
        
        // Pass towards opponent's goal
        let targetX, targetY;
        
        if (player.team === Teams.TEAM_1) {
            targetX = bounds.x + bounds.width * 0.75;
        } else {
            targetX = bounds.x + bounds.width * 0.25;
        }
        
        targetY = player.y + (Math.random() - 0.5) * 100;
        
        player.passBall(targetX, targetY);
        console.log(`Player ${player.number} makes forward pass`);
    }
    
    performDefensiveAction() {
        const player = this.scene.currentPlayer;
        const ball = this.scene.ball;
        
        if (!ball.isHeld) {
            // Try to get loose ball
            const distance = player.getDistanceTo(ball.x, ball.y);
            if (distance < 100) {
                player.moveTowards(ball.x, ball.y);
            }
        } else if (ball.holder && ball.holder.team !== player.team) {
            // Apply pressure to ball carrier
            player.moveTowards(ball.holder.x, ball.holder.y);
        }
    }
    
    handlePointerAction(pointer) {
        if (this.scene.isPaused) return;
        
        // Convert screen coordinates to world coordinates
        const worldPoint = this.scene.cameras.main.getWorldPoint(pointer.x, pointer.y);
        
        if (this.scene.currentPlayer && this.scene.currentPlayer.hasBall) {
            // Direct pass/shoot to clicked location
            if (this.isInGoalArea(worldPoint.x, worldPoint.y)) {
                this.scene.currentPlayer.shootBall(worldPoint.x, worldPoint.y);
            } else {
                this.scene.currentPlayer.passBall(worldPoint.x, worldPoint.y);
            }
        } else if (this.scene.currentPlayer) {
            // Move to clicked location
            this.scene.currentPlayer.moveTowards(worldPoint.x, worldPoint.y);
        }
    }
    
    handleRightClick(pointer) {
        // Right click to switch player
        this.scene.switchPlayer();
    }
    
    isInGoalArea(x, y) {
        const bounds = this.scene.poolBounds;
        const goalThreshold = 100;
        
        return x > bounds.x + bounds.width - goalThreshold || x < bounds.x + goalThreshold;
    }
    
    // Touch control methods
    setTouchMovement(x, y) {
        this.touchInput.movementX = x;
        this.touchInput.movementY = y;
        this.touchInput.isActive = (x !== 0 || y !== 0);
    }
    
    triggerTouchAction(action) {
        const currentTime = this.scene.time.now;
        
        if (currentTime - this.lastActionTime < this.actionCooldown) return;
        
        switch (action) {
            case 'pass':
                if (this.scene.currentPlayer && this.scene.currentPlayer.hasBall) {
                    this.performPass();
                }
                break;
            case 'shoot':
                if (this.scene.currentPlayer && this.scene.currentPlayer.hasBall) {
                    this.performShoot();
                }
                break;
            case 'switch':
                this.scene.switchPlayer();
                break;
        }
        
        this.lastActionTime = currentTime;
    }
    
    // Utility methods
    isKeyDown(keyName) {
        return this.keys[keyName] && this.keys[keyName].isDown;
    }
    
    wasKeyJustPressed(keyName) {
        return this.keys[keyName] && Phaser.Input.Keyboard.JustDown(this.keys[keyName]);
    }
}
