class GameRules {
    constructor(scene) {
        this.scene = scene;
        
        // Game state tracking
        this.currentPossession = Teams.TEAM_1;
        this.lastPossessionChange = 0;
        this.shotClockStartTime = 0;
        
        // Foul tracking
        this.fouls = {
            [Teams.TEAM_1]: [],
            [Teams.TEAM_2]: []
        };
        
        // Exclusion tracking
        this.exclusions = [];
        
        // Game events
        this.events = [];
    }
    
    update(time, delta) {
        // Update exclusions
        this.updateExclusions(delta);
        
        // Check for rule violations
        this.checkRuleViolations();
        
        // Update shot clock
        this.updateShotClockRules();
    }
    
    updateExclusions(delta) {
        this.exclusions = this.exclusions.filter(exclusion => {
            exclusion.timeLeft -= delta / 1000;
            
            if (exclusion.timeLeft <= 0) {
                // Return player from exclusion
                exclusion.player.returnFromExclusion();
                return false; // Remove from array
            }
            
            return true; // Keep in array
        });
    }
    
    checkRuleViolations() {
        // Check for various rule violations
        this.checkOffside();
        this.checkIllegalPosition();
        this.checkBallUnderwater();
        this.checkGoalkeeperViolations();
    }
    
    checkOffside() {
        // Water polo doesn't have traditional offside, but check for illegal positioning
        // This is a simplified implementation
    }
    
    checkIllegalPosition() {
        // Check if players are in illegal positions
        const bounds = this.scene.poolBounds;
        const twoMeterLine = GameConfig.POOL.TWO_METER_LINE;
        
        this.scene.players.forEach(player => {
            if (player.isExcluded) return;
            
            // Check 2-meter rule (simplified)
            const isInTwoMeterArea = this.isPlayerInTwoMeterArea(player);
            
            if (isInTwoMeterArea && !this.canPlayerBeInTwoMeterArea(player)) {
                this.callFoul(player, 'illegal_position');
            }
        });
    }
    
    isPlayerInTwoMeterArea(player) {
        const bounds = this.scene.poolBounds;
        const twoMeterLine = GameConfig.POOL.TWO_METER_LINE;
        
        if (player.team === Teams.TEAM_1) {
            return player.x > bounds.x + bounds.width - twoMeterLine;
        } else {
            return player.x < bounds.x + twoMeterLine;
        }
    }
    
    canPlayerBeInTwoMeterArea(player) {
        // Players can be in 2m area if:
        // 1. They are defending
        // 2. Ball is in the area
        // 3. They are the goalkeeper
        
        if (player.isGoalkeeper()) return true;
        
        const ball = this.scene.ball;
        if (ball && this.isPlayerInTwoMeterArea({ x: ball.x, y: ball.y, team: player.team })) {
            return true;
        }
        
        // Check if defending (opponent has ball in their area)
        if (ball && ball.holder && ball.holder.team !== player.team) {
            return this.isPlayerInTwoMeterArea(ball.holder);
        }
        
        return false;
    }
    
    checkBallUnderwater() {
        // In real water polo, keeping ball underwater is illegal
        // This is a simplified check
        const ball = this.scene.ball;
        
        if (ball && ball.holder) {
            // Check if player has held ball too long without action
            const holdTime = this.scene.time.now - ball.lastTouchTime;
            if (holdTime > 5000) { // 5 seconds
                this.callFoul(ball.holder, 'ball_underwater');
            }
        }
    }
    
    checkGoalkeeperViolations() {
        this.scene.players.forEach(player => {
            if (!player.isGoalkeeper()) return;
            
            // Check if goalkeeper is outside their area with special privileges
            if (!player.isInGoalArea() && player.hasBall) {
                // Goalkeeper loses special privileges outside 6m area
                // This is handled in player movement/actions
            }
        });
    }
    
    updateShotClockRules() {
        // Shot clock management is handled in GameScene
        // This method can be used for additional shot clock rules
    }
    
    // Foul system
    callFoul(player, foulType, severity = 'minor') {
        const foul = {
            player: player,
            type: foulType,
            severity: severity,
            time: this.scene.time.now,
            quarter: this.scene.currentQuarter
        };
        
        this.fouls[player.team].push(foul);
        this.events.push({
            type: 'foul',
            data: foul,
            time: this.scene.time.now
        });
        
        console.log(`Foul called on player ${player.number}: ${foulType} (${severity})`);
        
        // Handle foul consequences
        this.handleFoulConsequences(foul);
    }
    
    handleFoulConsequences(foul) {
        switch (foul.severity) {
            case 'minor':
                this.handleMinorFoul(foul);
                break;
            case 'major':
                this.handleMajorFoul(foul);
                break;
            case 'misconduct':
                this.handleMisconductFoul(foul);
                break;
        }
    }
    
    handleMinorFoul(foul) {
        // Minor foul: turnover
        const opposingTeam = foul.player.team === Teams.TEAM_1 ? Teams.TEAM_2 : Teams.TEAM_1;
        this.changePossession(opposingTeam);
        
        // Reset shot clock
        this.resetShotClock();
    }
    
    handleMajorFoul(foul) {
        // Major foul: exclusion + turnover
        this.excludePlayer(foul.player, GameConfig.RULES.EXCLUSION_TIME);
        
        const opposingTeam = foul.player.team === Teams.TEAM_1 ? Teams.TEAM_2 : Teams.TEAM_1;
        this.changePossession(opposingTeam);
        
        // Reset shot clock to 18 seconds (after exclusion)
        this.resetShotClock(GameConfig.RULES.SHOT_CLOCK_REBOUND);
    }
    
    handleMisconductFoul(foul) {
        // Misconduct: longer exclusion
        this.excludePlayer(foul.player, GameConfig.RULES.EXCLUSION_TIME * 2);
        
        const opposingTeam = foul.player.team === Teams.TEAM_1 ? Teams.TEAM_2 : Teams.TEAM_1;
        this.changePossession(opposingTeam);
    }
    
    excludePlayer(player, duration) {
        player.exclude(duration);
        
        this.exclusions.push({
            player: player,
            timeLeft: duration,
            startTime: this.scene.time.now
        });
        
        this.events.push({
            type: 'exclusion',
            data: { player: player, duration: duration },
            time: this.scene.time.now
        });
        
        console.log(`Player ${player.number} excluded for ${duration} seconds`);
    }
    
    // Possession management
    changePossession(team) {
        this.currentPossession = team;
        this.lastPossessionChange = this.scene.time.now;
        
        this.events.push({
            type: 'possession_change',
            data: { team: team },
            time: this.scene.time.now
        });
        
        console.log(`Possession changed to team ${team + 1}`);
    }
    
    startPossession(team) {
        this.changePossession(team);
        this.startShotClock();
    }
    
    // Shot clock management
    startShotClock(duration = GameConfig.RULES.SHOT_CLOCK_NORMAL) {
        this.scene.shotClock = duration;
        this.scene.shotClockActive = true;
        this.shotClockStartTime = this.scene.time.now;
        
        console.log(`Shot clock started: ${duration} seconds`);
    }
    
    resetShotClock(duration = GameConfig.RULES.SHOT_CLOCK_REBOUND) {
        this.scene.shotClock = duration;
        this.shotClockStartTime = this.scene.time.now;
        
        console.log(`Shot clock reset: ${duration} seconds`);
    }
    
    stopShotClock() {
        this.scene.shotClockActive = false;
        console.log('Shot clock stopped');
    }
    
    shotClockViolation() {
        console.log('Shot clock violation!');
        
        // Change possession
        const opposingTeam = this.currentPossession === Teams.TEAM_1 ? Teams.TEAM_2 : Teams.TEAM_1;
        this.changePossession(opposingTeam);
        
        // Reset shot clock
        this.resetShotClock();
        
        this.events.push({
            type: 'shot_clock_violation',
            data: { team: this.currentPossession },
            time: this.scene.time.now
        });
    }
    
    // Goal handling
    resetAfterGoal() {
        // Reset positions
        this.resetPlayerPositions();
        
        // Reset ball to center
        this.scene.ball.resetToCenter();
        
        // Change possession to non-scoring team
        const lastGoalTeam = this.getLastGoalTeam();
        const newPossession = lastGoalTeam === Teams.TEAM_1 ? Teams.TEAM_2 : Teams.TEAM_1;
        this.startPossession(newPossession);
        
        this.events.push({
            type: 'goal_reset',
            data: { possession: newPossession },
            time: this.scene.time.now
        });
    }
    
    getLastGoalTeam() {
        // This would need to be tracked when goals are scored
        // For now, assume Team 1 scored last
        return Teams.TEAM_1;
    }
    
    resetPlayerPositions() {
        // Reset players to starting positions
        this.scene.teams.forEach((team, teamIndex) => {
            team.players.forEach((player, playerIndex) => {
                if (!player.isExcluded) {
                    // Reset to starting position based on team and position
                    const bounds = this.scene.poolBounds;
                    const side = teamIndex === 0 ? 1 : -1;
                    
                    let x, y;
                    
                    switch (player.position) {
                        case PlayerPositions.GOALKEEPER:
                            x = bounds.x + bounds.width / 2 + side * (bounds.width / 2 - 30);
                            y = bounds.y + bounds.height / 2;
                            break;
                        default:
                            x = bounds.x + bounds.width / 2 + side * (bounds.width / 4);
                            y = bounds.y + bounds.height / 2 + (playerIndex - 3) * 40;
                            break;
                    }
                    
                    player.setPosition(x, y);
                    player.stopMovement();
                }
            });
        });
    }
    
    // Utility methods
    getActivePlayers(team) {
        return this.scene.teams[team].players.filter(player => !player.isExcluded);
    }
    
    getExcludedPlayers(team) {
        return this.scene.teams[team].players.filter(player => player.isExcluded);
    }
    
    getFoulsForTeam(team) {
        return this.fouls[team];
    }
    
    getGameEvents() {
        return this.events;
    }
    
    // Game state queries
    isPlayerAdvantage(team) {
        const opposingTeam = team === Teams.TEAM_1 ? Teams.TEAM_2 : Teams.TEAM_1;
        const teamActivePlayers = this.getActivePlayers(team).length;
        const opposingActivePlayers = this.getActivePlayers(opposingTeam).length;
        
        return teamActivePlayers > opposingActivePlayers;
    }
    
    getPlayerAdvantageCount(team) {
        const opposingTeam = team === Teams.TEAM_1 ? Teams.TEAM_2 : Teams.TEAM_1;
        const teamActivePlayers = this.getActivePlayers(team).length;
        const opposingActivePlayers = this.getActivePlayers(opposingTeam).length;
        
        return Math.max(0, teamActivePlayers - opposingActivePlayers);
    }
}
