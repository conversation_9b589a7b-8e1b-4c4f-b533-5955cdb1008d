class PowerUpManager {
    constructor(scene) {
        this.scene = scene;
        
        // Power-up settings
        this.spawnRate = GameConfig.ARCADE.POWER_UP_SPAWN_RATE * 1000; // Convert to milliseconds
        this.lastSpawnTime = 0;
        this.maxPowerUps = 3; // Maximum power-ups on field at once
        
        // Active power-ups
        this.activePowerUps = [];
        
        // Power-up types
        this.powerUpTypes = [
            {
                type: 'turbo',
                sprite: 'powerup-turbo',
                duration: GameConfig.ARCADE.TURBO_DURATION,
                color: 0x00FF00,
                name: 'TURBO SWIM',
                description: 'Increased swimming speed'
            },
            {
                type: 'fastShot',
                sprite: 'powerup-fastshot',
                duration: GameConfig.ARCADE.FAST_SHOT_DURATION,
                color: 0xFF0000,
                name: 'FAST SHOT',
                description: 'Increased shot power'
            }
        ];
        
        // Visual effects
        this.effects = [];
        
        this.setupSpawnTimer();
    }
    
    setupSpawnTimer() {
        if (!this.scene.arcadeMode) return;
        
        this.spawnTimer = this.scene.time.addEvent({
            delay: this.spawnRate,
            callback: this.trySpawnPowerUp,
            callbackScope: this,
            loop: true
        });
    }
    
    update(time, delta) {
        if (!this.scene.arcadeMode) return;
        
        // Update active power-ups
        this.updateActivePowerUps(time, delta);
        
        // Update visual effects
        this.updateEffects(time, delta);
        
        // Check for power-up collection
        this.checkPowerUpCollection();
    }
    
    trySpawnPowerUp() {
        if (this.activePowerUps.length >= this.maxPowerUps) return;
        
        // Random chance to spawn
        if (Math.random() < 0.7) { // 70% chance
            this.spawnRandomPowerUp();
        }
    }
    
    spawnRandomPowerUp() {
        const bounds = this.scene.poolBounds;
        
        // Choose random power-up type
        const powerUpType = this.powerUpTypes[Math.floor(Math.random() * this.powerUpTypes.length)];
        
        // Choose random position (avoid goals and edges)
        const margin = 80;
        const x = bounds.x + margin + Math.random() * (bounds.width - margin * 2);
        const y = bounds.y + margin + Math.random() * (bounds.height - margin * 2);
        
        this.spawnPowerUp(powerUpType.type, x, y);
    }
    
    spawnPowerUp(type, x, y) {
        const powerUpData = this.powerUpTypes.find(p => p.type === type);
        if (!powerUpData) return;
        
        // Create power-up sprite
        const powerUp = this.scene.add.image(x, y, powerUpData.sprite);
        powerUp.setScale(1.2);
        powerUp.setDepth(500);
        
        // Add physics
        this.scene.physics.add.existing(powerUp);
        powerUp.body.setCircle(15);
        
        // Add glow effect
        const glow = this.scene.add.circle(x, y, 20, powerUpData.color, 0.3);
        glow.setDepth(499);
        
        // Add floating animation
        this.scene.tweens.add({
            targets: [powerUp, glow],
            y: y - 10,
            duration: 1500,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
        
        // Add rotation animation
        this.scene.tweens.add({
            targets: powerUp,
            rotation: Math.PI * 2,
            duration: 3000,
            repeat: -1,
            ease: 'Linear'
        });
        
        // Add pulsing glow
        this.scene.tweens.add({
            targets: glow,
            alpha: 0.1,
            scale: 1.2,
            duration: 1000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
        
        // Store power-up data
        const powerUpObject = {
            sprite: powerUp,
            glow: glow,
            type: type,
            data: powerUpData,
            spawnTime: this.scene.time.now,
            collected: false
        };
        
        this.activePowerUps.push(powerUpObject);
        
        console.log(`Power-up spawned: ${powerUpData.name} at (${Math.round(x)}, ${Math.round(y)})`);
    }
    
    updateActivePowerUps(time, delta) {
        this.activePowerUps = this.activePowerUps.filter(powerUp => {
            if (powerUp.collected) {
                return false; // Remove collected power-ups
            }
            
            // Remove power-ups after 30 seconds if not collected
            const age = time - powerUp.spawnTime;
            if (age > 30000) {
                this.removePowerUp(powerUp);
                return false;
            }
            
            return true;
        });
    }
    
    updateEffects(time, delta) {
        this.effects = this.effects.filter(effect => {
            effect.timeLeft -= delta / 1000;
            
            if (effect.timeLeft <= 0) {
                if (effect.cleanup) {
                    effect.cleanup();
                }
                return false;
            }
            
            if (effect.update) {
                effect.update(delta);
            }
            
            return true;
        });
    }
    
    checkPowerUpCollection() {
        this.activePowerUps.forEach(powerUp => {
            if (powerUp.collected) return;
            
            // Check collision with all players
            this.scene.players.forEach(player => {
                if (player.isExcluded) return;
                
                const distance = GameUtils.getDistance(
                    player.x, player.y,
                    powerUp.sprite.x, powerUp.sprite.y
                );
                
                if (distance < 25) { // Collection radius
                    this.collectPowerUp(powerUp, player);
                }
            });
        });
    }
    
    collectPowerUp(powerUp, player) {
        if (powerUp.collected) return;
        
        powerUp.collected = true;
        
        // Apply power-up to player
        player.activatePowerUp(powerUp.type, powerUp.data.duration);
        
        // Create collection effect
        this.createCollectionEffect(powerUp.sprite.x, powerUp.sprite.y, powerUp.data.color);
        
        // Show notification
        this.showPowerUpNotification(powerUp.data, player);
        
        // Remove power-up visuals
        this.removePowerUp(powerUp);
        
        console.log(`Player ${player.number} collected ${powerUp.data.name} power-up`);
    }
    
    createCollectionEffect(x, y, color) {
        // Particle burst effect
        const particles = [];
        const particleCount = 12;
        
        for (let i = 0; i < particleCount; i++) {
            const angle = (i / particleCount) * Math.PI * 2;
            const speed = 100 + Math.random() * 50;
            
            const particle = this.scene.add.circle(x, y, 3, color);
            particle.setDepth(600);
            
            // Animate particle
            this.scene.tweens.add({
                targets: particle,
                x: x + Math.cos(angle) * speed,
                y: y + Math.sin(angle) * speed,
                alpha: 0,
                scale: 0,
                duration: 800,
                ease: 'Quad.easeOut',
                onComplete: () => {
                    particle.destroy();
                }
            });
            
            particles.push(particle);
        }
        
        // Central flash
        const flash = this.scene.add.circle(x, y, 30, 0xFFFFFF, 0.8);
        flash.setDepth(601);
        
        this.scene.tweens.add({
            targets: flash,
            scale: 2,
            alpha: 0,
            duration: 300,
            ease: 'Quad.easeOut',
            onComplete: () => {
                flash.destroy();
            }
        });
    }
    
    showPowerUpNotification(powerUpData, player) {
        const width = this.scene.cameras.main.width;
        const height = this.scene.cameras.main.height;
        
        // Create notification container
        const notification = this.scene.add.container(width / 2, height / 2 - 100);
        notification.setDepth(1500);
        
        // Background
        const bg = this.scene.add.rectangle(0, 0, 300, 80, 0x000000, 0.8);
        bg.setStrokeStyle(3, powerUpData.color);
        
        // Power-up name
        const nameText = this.scene.add.text(0, -15, powerUpData.name, {
            fontSize: '20px',
            fontFamily: 'Arial',
            color: '#ffffff',
            fontStyle: 'bold'
        }).setOrigin(0.5);
        
        // Description
        const descText = this.scene.add.text(0, 10, powerUpData.description, {
            fontSize: '14px',
            fontFamily: 'Arial',
            color: '#ffffff'
        }).setOrigin(0.5);
        
        // Player info
        const playerText = this.scene.add.text(0, 30, `Player ${player.number}`, {
            fontSize: '12px',
            fontFamily: 'Arial',
            color: powerUpData.color === 0x00FF00 ? '#00FF00' : '#FF0000'
        }).setOrigin(0.5);
        
        notification.add([bg, nameText, descText, playerText]);
        
        // Animate notification
        notification.setScale(0);
        this.scene.tweens.add({
            targets: notification,
            scale: 1,
            duration: 300,
            ease: 'Back.easeOut'
        });
        
        // Auto-remove after 2 seconds
        this.scene.time.delayedCall(2000, () => {
            this.scene.tweens.add({
                targets: notification,
                alpha: 0,
                scale: 0.8,
                duration: 300,
                ease: 'Quad.easeIn',
                onComplete: () => {
                    notification.destroy();
                }
            });
        });
    }
    
    removePowerUp(powerUp) {
        if (powerUp.sprite) {
            powerUp.sprite.destroy();
        }
        if (powerUp.glow) {
            powerUp.glow.destroy();
        }
    }
    
    // Player power-up effect methods
    createPlayerEffect(player, type) {
        let effect = null;
        
        switch (type) {
            case 'turbo':
                effect = this.createTurboEffect(player);
                break;
            case 'fastShot':
                effect = this.createFastShotEffect(player);
                break;
        }
        
        if (effect) {
            this.effects.push(effect);
        }
        
        return effect;
    }
    
    createTurboEffect(player) {
        // Create speed trail effect
        const trail = this.scene.add.graphics();
        trail.setDepth(player.depth - 1);
        
        const effect = {
            type: 'turbo',
            player: player,
            trail: trail,
            timeLeft: GameConfig.ARCADE.TURBO_DURATION,
            trailPoints: [],
            update: (delta) => {
                // Update trail
                this.updateSpeedTrail(effect);
            },
            cleanup: () => {
                trail.destroy();
            }
        };
        
        return effect;
    }
    
    createFastShotEffect(player) {
        // Create power aura effect
        const aura = this.scene.add.circle(player.x, player.y, 25, 0xFF0000, 0.3);
        aura.setDepth(player.depth - 1);
        
        // Pulsing animation
        this.scene.tweens.add({
            targets: aura,
            scale: 1.2,
            alpha: 0.1,
            duration: 500,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
        
        const effect = {
            type: 'fastShot',
            player: player,
            aura: aura,
            timeLeft: GameConfig.ARCADE.FAST_SHOT_DURATION,
            update: (delta) => {
                // Update aura position
                aura.setPosition(player.x, player.y);
            },
            cleanup: () => {
                aura.destroy();
            }
        };
        
        return effect;
    }
    
    updateSpeedTrail(effect) {
        const player = effect.player;
        const trail = effect.trail;
        
        // Add current position to trail
        effect.trailPoints.push({ x: player.x, y: player.y, time: this.scene.time.now });
        
        // Remove old points (older than 200ms)
        const currentTime = this.scene.time.now;
        effect.trailPoints = effect.trailPoints.filter(point => 
            currentTime - point.time < 200
        );
        
        // Draw trail
        trail.clear();
        if (effect.trailPoints.length > 1) {
            trail.lineStyle(4, 0x00FF00, 0.6);
            trail.beginPath();
            trail.moveTo(effect.trailPoints[0].x, effect.trailPoints[0].y);
            
            for (let i = 1; i < effect.trailPoints.length; i++) {
                const alpha = i / effect.trailPoints.length;
                trail.lineStyle(4 * alpha, 0x00FF00, 0.6 * alpha);
                trail.lineTo(effect.trailPoints[i].x, effect.trailPoints[i].y);
            }
            
            trail.strokePath();
        }
    }
    
    // Utility methods
    clearAllPowerUps() {
        this.activePowerUps.forEach(powerUp => {
            this.removePowerUp(powerUp);
        });
        this.activePowerUps = [];
    }
    
    getPowerUpCount() {
        return this.activePowerUps.length;
    }
    
    isArcadeModeActive() {
        return this.scene.arcadeMode;
    }
    
    destroy() {
        this.clearAllPowerUps();
        
        if (this.spawnTimer) {
            this.spawnTimer.destroy();
        }
        
        this.effects.forEach(effect => {
            if (effect.cleanup) {
                effect.cleanup();
            }
        });
        this.effects = [];
    }
}
