# Water Polo Clash

A 2D top-down water polo game built with Phaser 3, featuring realistic water polo rules and gameplay mechanics.

## Features

### Core Gameplay
- **Authentic Water Polo Rules**: Based on World Aquatics regulations
- **Pool Dimensions**: Scaled 25.6m × 20m pool with proper zone markings
- **Team Composition**: 7 players per team (including 1 goalkeeper)
- **Game Structure**: 4 quarters, 2 minutes each (configurable)
- **Shot Clock**: 28-second shot clock, 18 seconds after rebounds/exclusions
- **Fouls & Exclusions**: 20-second exclusions for major fouls
- **Goalkeeper Rules**: Special privileges within 6m area

### Game Modes
- **Single Player vs AI**: Play against intelligent AI opponents
- **Arcade Mode**: Enhanced gameplay with power-ups and special effects
  - Turbo Swim: Increased swimming speed
  - Fast Shot: Enhanced shooting power

### Controls

#### Desktop (Keyboard)
- **WASD**: Move player
- **SPACE**: Pass ball / Shoot at goal
- **TAB**: Switch to another player
- **ESC**: Pause game
- **Mouse Click**: Direct pass/shoot to location

#### Mobile (Touch)
- **Virtual D-Pad**: Movement control
- **Action Buttons**: Pass, Shoot, Switch Player
- **Touch Screen**: Direct pass/shoot to touched location

### Technical Features
- **Responsive Design**: Works on desktop and mobile devices
- **Physics Engine**: Realistic ball and player physics with water resistance
- **AI System**: Intelligent teammates and opponents with strategic positioning
- **Visual Indicators**: 2m and 6m zone markings, possession indicators
- **Real-time HUD**: Score, timer, shot clock, exclusions display

## Installation & Setup

### Prerequisites
- Node.js (v14 or higher)
- Modern web browser

### Quick Start
1. Clone or download the project
2. Install dependencies:
   ```bash
   npm install
   ```
3. Start the development server:
   ```bash
   npm run dev
   ```
4. Open your browser to `http://localhost:8080`

### Alternative Setup
You can also run the game by simply opening `index.html` in a modern web browser, as it uses CDN-hosted Phaser 3.

## Game Rules

### Basic Rules
- Score goals by getting the ball into the opponent's goal
- Players can swim freely but cannot touch the bottom (except goalkeepers in 6m area)
- Ball must be passed or shot within 28 seconds of possession
- Major fouls result in 20-second player exclusions

### Goalkeeper Rules
- Can use both hands within 6m area
- Can touch the bottom within 6m area
- Loses special privileges outside 6m area

### Fouls
- **Minor Fouls**: Result in turnover
- **Major Fouls**: Result in exclusion + turnover
- **Exclusions**: Player must leave pool for 20 seconds

## File Structure

```
waterpolo_clash/
├── index.html              # Main HTML file
├── package.json            # Project dependencies
├── js/
│   ├── config.js           # Game configuration
│   ├── main.js             # Game initialization
│   ├── scenes/
│   │   ├── PreloadScene.js # Asset loading
│   │   ├── MainMenuScene.js# Main menu
│   │   └── GameScene.js    # Main game scene
│   ├── entities/
│   │   ├── Player.js       # Player entity
│   │   └── Ball.js         # Ball entity
│   ├── systems/
│   │   ├── InputManager.js # Input handling
│   │   ├── GameRules.js    # Rules engine
│   │   ├── AIManager.js    # AI system
│   │   └── PowerUpManager.js # Arcade mode power-ups
│   └── ui/
│       └── HUD.js          # User interface
└── assets/                 # Game assets (generated programmatically)
```

## Configuration

Game settings can be modified in `js/config.js`:

- **Pool dimensions and scaling**
- **Player speeds and physics**
- **Game timing (quarters, shot clock)**
- **AI difficulty and behavior**
- **Visual settings and colors**

## Browser Compatibility

- Chrome/Chromium (recommended)
- Firefox
- Safari
- Edge
- Mobile browsers (iOS Safari, Chrome Mobile)

## Performance

The game is optimized for both desktop and mobile devices:
- Automatic quality reduction on mobile
- Efficient physics calculations
- Optimized rendering for 60 FPS gameplay

## Development

### Adding New Features
1. Core game logic goes in `js/systems/`
2. Visual entities go in `js/entities/`
3. UI components go in `js/ui/`
4. Configuration in `js/config.js`

### Debugging
Set `DEBUG_MODE: true` in `js/config.js` to enable physics debugging visuals.

## Credits

- Built with [Phaser 3](https://phaser.io/) game framework
- Water polo rules based on [World Aquatics](https://www.worldaquatics.com/) regulations
- Responsive design for cross-platform compatibility

## License

MIT License - Feel free to modify and distribute.

---

**Water Polo Clash** - Experience the excitement of water polo in your browser!
